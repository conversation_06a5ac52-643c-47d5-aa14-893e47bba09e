import "./webTechnologies.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  web_technology_img1,
  web_technology_img2,
  web_technology_img3,
  web_technology_img4,
  web_technology_img5,
  web_technology_img6,
  web_technology_img7,
  web_technology_img8,
  web_technology_img9,
  web_technology_img10,
  web_technology_img11,
  web_technology_img12,
  web_technology_img14,
  web_technology_img17,
  FullContentWeb,
} from "@/public/index";
import {
  webTechnologyOfferings,
  webTechnologiesContent,
} from "@/constant/index";
import OfferingPairs from "@/components/OfferingPairs/OfferingPairs";
import ExpandedServices from "@/components/ExpandedServices/expandedservices";

const WebTechnologiesPage = () => {
  return (
    <Box className="web-technologies-page-container">
      <Box className="web-technologies-page-content">
        <Box className="web-technologies-header-image">
          <Image
            src={web_technology_img1}
            alt="Web Technologies"
            data-aos="fade-up"
            data-aos-duration="1000"
          />
        </Box>

        <Box className="web-technologies-content">
          <Typography
            variant="h2"
            className="web-technologies-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {webTechnologiesContent.title}
          </Typography>
          <Box className="web-technologies-description-container">
            <Typography
              variant="body1"
              className="web-technologies-description"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="100"
            >
              {webTechnologiesContent.description.part1}
            </Typography>
            <Typography
              variant="body1"
              className="web-technologies-description"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="200"
            >
              {webTechnologiesContent.description.part2}
            </Typography>
            {/* <Typography
              variant="body1"
              className="web-technologies-description"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="300"
            >
              {webTechnologiesContent.description.part3}
            </Typography> */}
          </Box>

          <Box className="web-technologies-images">
            <Image
              src={web_technology_img2}
              alt="Web Technologies"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="100"
            />
            <Image
              src={web_technology_img3}
              alt="Web Technologies"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="200"
            />
            <Image
              src={web_technology_img4}
              alt="Web Technologies"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="300"
            />
            <Image
              src={web_technology_img5}
              alt="Web Technologies"
              data-aos="fade-up"
              data-aos-duration="1000"
              data-aos-delay="400"
            />
          </Box>
        </Box>

        <Box className="our-offerings">
          <Typography
            variant="h2"
            className="our-offerings-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {webTechnologiesContent.offerings.title}
          </Typography>
          <Typography
            variant="body1"
            className="our-offerings-description"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="100"
          >
            {webTechnologiesContent.offerings.description}
          </Typography>
          <OfferingPairs
            offerings={webTechnologyOfferings}
            className="our-offerings-list"
          />
        </Box>

        <Box className="our-expanded-services-includes">
          <Typography
            variant="h2"
            className="our-expanded-services-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {webTechnologiesContent.expandedServices.title}
          </Typography>
          {/* <Box className="our-expanded-services-list">
            <Box className="expanded-services-item1">
              <ExpandedServices
                icon={
                  web_technology_img14
                }
                header="Content Management System (CMS) "
                description="A CMS allows you to edit, publish, and maintain the web content such as text, images, videos, and other media on your web portal with minimal involvement of dev team. Our team is proficient in developing and customizing CMS solutions, enabling you to manage your website content effortlessly and efficiently."
              />
            </Box>
            <Box className="expanded-services-item2">
              <Box className="expanded-services-item2-box1">
                <ExpandedServices
                  icon={
                    web_technology_img14
                  }
                  header="E-commerce Development"
                  description="We specialize in creating robust e-commerce platforms that provide seamless shopping experiences, integrating secure payment gateways, and ensuring scalability to support your business growth."
                />
              </Box>
              <Box className="expanded-services-item2-box2">
                <ExpandedServices
                  icon={
                    web_technology_img14
                  }
                  header="Application Modernization Services"
                  description="We specialize in modernizing legacy applications to boost their performance, strengthen security, and ensure seamless compatibility with today’s technologies."
                />
              </Box>
            </Box>
            <Box className="expanded-services-item3">
              <Box className="expanded-services-item3-box1">
                <ExpandedServices
                  icon={
                   web_technology_img14
                  }
                  header="Search Engine Optimization (SEO)"
                  description="Our SEO specialists craft tailored strategies to enhance your website’s search engine visibility, driving organic traffic and expanding your online presence."
                />
              </Box>
              <Box className="expanded-services-item3-box2">
                <ExpandedServices
                  icon={
                    web_technology_img17
                  }
                  header="DevOps Service"
                  description="We provide cloud infrastructure support services for seamless performance and sustainable growth to meet your business requirements "
                />
              </Box>
            </Box>
            <Box className="expanded-services-item4">
              <ExpandedServices
                icon={
                  web_technology_img17
                }
                header="Micro-services Development Support"
                description="A microservices-based software applications built on a modular architecture of small, independent services. Each microservice is designed around a specific business capability, allowing for seamless development, deployment, and scaling."
              />
            </Box>
          </Box> */}
          <Box className="full-content-web">
            <Image src={FullContentWeb} alt="Full Content Web" />
          </Box>
        </Box>

        <Box className="case-study">
          <Typography
            variant="h2"
            className="case-study-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {webTechnologiesContent.caseStudies.title}
          </Typography>
          <Box className="case-study-images">
            {webTechnologiesContent.caseStudies.items.map((item, index) => {
              const images = [
                web_technology_img9,
                web_technology_img10,
                web_technology_img11,
                web_technology_img12,
              ];
              return (
                <Box key={index} className="case-study-image-item">
                  <Image
                    src={images[index]}
                    alt="Web Technologies"
                    data-aos="fade-up"
                    data-aos-duration="1000"
                    data-aos-delay={100 * (index + 1)}
                  />
                  <Typography
                    className="image-title"
                    data-aos="fade-up"
                    data-aos-duration="1000"
                    data-aos-delay={100 * (index + 1)}
                  >
                    {item.title}
                  </Typography>
                </Box>
              );
            })}
          </Box>

          <Box className="case-study-description">
            <Typography variant="body1" className="case-study-text">
              Elevate Your Online Presence with Aadvik TekLabs’ Expert Web
              Design & Development Services. Connect with us to get cutting-edge
              web development service and build your solution that seamlessly
              combine aesthetics, functionality, and innovation.
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default WebTechnologiesPage;
