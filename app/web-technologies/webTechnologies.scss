.web-technologies-page-container {
  .web-technologies-page-content {
    .web-technologies-header-image {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        height: auto;
      }
    }

    .web-technologies-content {
      padding: 50px 70px;
      margin-bottom: 80px;

      .web-technologies-title {
        margin-bottom: 50px;
        min-height: 40px;
        font-family: Poppins;
        font-weight: 400;
        font-size: 36px;
        line-height: 30px;
        letter-spacing: 0%;
        background: #8cffe4;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .web-technologies-description-container {
        display: flex;
        flex-direction: column;
        gap: 40px;
        margin-bottom: 80px;

        .web-technologies-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
        }
      }

      .web-technologies-images {
        display: flex;
        gap: 15px;
        justify-content: center;
        align-items: center;
      }
    }

    .our-offerings {
      padding: 0px 70px;
      margin-bottom: 80px;

      .our-offerings-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 34px;
        letter-spacing: 0%;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 50px;
      }

      .our-offerings-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 50px;
      }

      .our-offerings-list {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .offering-item-wrapper {
          display: flex;
          gap: 15px;
        }

        .offering-item {
          padding: 38.09px 27.41px 39.58px 34px;
          width: 100%;
          min-height: 320px;
          border: 1px solid #2499e280;
          color: #ffffff;

          .offering-item-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 22px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: center;
            background: #8cffe4;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30.48px;
          }
        }
      }
    }

    .our-expanded-services-includes {
      padding: 0px 70px;
      margin-bottom: 80px;

      .our-expanded-services-title {
        font-family: Poppins;
        font-weight: 300;
        font-size: 34px;

        letter-spacing: 0%;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 50px;
      }

      .our-expanded-services-list {
        .expanded-services-item1 {
          padding-bottom: 15px;
          border-bottom: 1px solid #135c88;
        }

        .expanded-services-item2 {
          display: flex;
          padding-bottom: 15px;
          padding-top: 15px;
          border-bottom: 1px solid #135c88;

          .expanded-services-item2-box1 {
            width: 50%;
            padding-right: 20px;
            border-right: 1px solid #135c88;
          }

          .expanded-services-item2-box2 {
            width: 50%;
            padding-left: 20px;
          }
        }

        .expanded-services-item3 {
          display: flex;
          padding-top: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid #135c88;

          .expanded-services-item3-box1 {
            width: 50%;
            padding-right: 20px;
            border-right: 1px solid #135c88;
          }

          .expanded-services-item3-box2 {
            width: 50%;
            padding-left: 20px;
          }
        }

        .expanded-services-item4 {
          padding-top: 15px;
          padding-bottom: 15px;
        }
      }
      .full-content-web {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 100%;
          height: auto;
        }
      }
    }

    // .case-study {
    //     padding-left: 93px;
    //     padding-right: 103px;
    //     margin-bottom: 301px;

    //     .case-study-title {
    //         font-family: Poppins;
    //         font-weight: 400;
    //         font-size: 34px;

    //         letter-spacing: 0%;
    //         text-align: center;
    //         background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
    //         background-clip: text;
    //         -webkit-text-fill-color: transparent;
    //         margin-bottom: 61px;
    //     }

    //     .case-study-images {
    //         display: flex;
    //         justify-content: center;
    //         align-items: center;
    //         gap: 59px;
    //         margin-bottom: 77.17px;

    //         img {
    //             width: 100%;
    //             max-width: 270px;
    //             min-height: 200px;
    //             border-radius: 10px;
    //         }

    //         .case-study-image-item {
    //             display: flex;
    //             flex-direction: column;
    //             align-items: center;
    //             gap: 44px;

    //             .image-title {
    //                 font-family: Montserrat;
    //                 font-weight: 400;
    //                 font-size: 24px;

    //                 line-height: 30px;
    //                 letter-spacing: 0%;
    //                 vertical-align: bottom;
    //                 color: #FFFFFF;
    //             }
    //         }
    //     }

    //     .case-study-description {
    //         .case-study-text {
    //             font-family: Poppins;
    //             font-weight: 400;
    //             font-style: italic;
    //             font-size: 16px;

    //             line-height: 30px;
    //             letter-spacing: 0%;
    //             color: #FFFFFF;
    //         }
    //     }
    // }

    .case-study {
      margin-top: 80px;
      padding-left: 93px;
      padding-right: 103px;
      margin-bottom: 150px;

      .case-study-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 34px;

        letter-spacing: 0%;
        text-align: center;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 61px;
      }

      .case-study-images {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 59px;
        margin-bottom: 77.17px;

        img {
          width: 100%;
          max-width: 270px;
          min-height: 200px;
          border-radius: 10px;
          transition: all 0.3s ease-in-out;
        }

        .case-study-image-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 44px;
          transition: all 0.3s ease-in-out;
          cursor: pointer;
          padding: 10px;
          border-radius: 12px;

          &:hover {
            transform: translateY(-10px);
            background: rgba(140, 255, 228, 0.05);

            img {
              transform: scale(1.05);
              box-shadow: 0 10px 20px rgba(140, 255, 228, 0.2);
            }

            .image-title {
              background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          img {
            transition: all 0.3s ease-in-out;
          }

          .image-title {
            font-family: Montserrat;
            font-weight: 400;
            font-size: 24px;

            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: bottom;
            color: #ffffff;
          }
        }
      }

      .case-study-description {
        display: flex;
        justify-content: center;
        align-items: center;

        .case-study-text {
          width: 80%;
          font-family: Poppins;
          font-weight: 400;
          font-style: italic !important;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          color: #ffffff;
        }
      }
    }
  }
}
