"use client";
import "./AR_VR.scss";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { Box, List, ListItem, Typography } from "@mui/material";
import React from "react";
import { AR_VR_Img1, AR_VR_Img2, AR_VR_Img3 } from "@/public/index";
import { arVrPageContent } from "@/constant/index";
import AR_VR_PopUp from "./AR_VR_PopUp";

function AR_VRpage() {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [showBlink, setShowBlink] = useState(false);
  const creativeImageRef = useRef<HTMLDivElement>(null);

  const handleOpenPopup = () => setIsPopupOpen(true);
  const handleClosePopup = () => setIsPopupOpen(false);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setShowBlink(entry.isIntersecting);
        });
      },
      { threshold: 0.5 }
    );
    if (creativeImageRef.current) {
      observer.observe(creativeImageRef.current);
    }
    return () => {
      if (creativeImageRef.current) {
        observer.unobserve(creativeImageRef.current);
      }
    };
  }, []);

  return (
    <Box className="ar-vr-page">
      <Box className="ar-vr-content">
        <Box className="ar-vr-image" data-aos="fade-up">
          <Image src={AR_VR_Img1} alt="AR/VR" />
        </Box>

        <Box className="ar-vr-text">
          <Typography
            className="ar-vr-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.mainSection.title}
          </Typography>
          <Typography
            className="ar-vr-description"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.mainSection.description}
          </Typography>
        </Box>

        {/* <Box className="ar-vr-text">
          <Typography className="ar-vr-description">
            {arVrPageContent.mainSection.description}
          </Typography>
        </Box> */}
        <Box className="future-vision">
          <Typography
            className="future-vision-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.futureVision.title}
          </Typography>
          <Typography
            className="future-vision-description"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.futureVision.description}
          </Typography>
          <Typography
            className="future-vision-description1"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.futureVision.description1}
          </Typography>

          <Box className="future-vision-bullets">
            <List className="future-vision-bullets-list">
              {arVrPageContent.futureVision.bullets.map((bullet, index) => (
                <ListItem key={index} className="future-vision-bullets-item">
                  <Box component="span" style={{ display: "inline" }}>
                    {/* {bullet.title && (
                      <Typography
                        component="span"
                        className="future-vision-bullets-title"
                        data-aos="fade-up"
                        data-aos-duration="1000"
                      >
                        {bullet.title}
                      </Typography>
                    )} */}
                    <Typography
                      component="span"
                      className="future-vision-bullets-text"
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      {bullet.text}
                    </Typography>
                  </Box>
                </ListItem>
              ))}
            </List>
            {/*  <Typography
              className="future-vision-conclusion"
              data-aos="fade-up"
              data-aos-duration="1000"
            >
              {arVrPageContent.futureVision.conclusion}
            </Typography> */}
          </Box>
        </Box>

        <Box className="what-we-offer">
          <Typography
            className="what-we-offer-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.whatWeOffer.title}
          </Typography>
          <Typography
            className="what-we-offer-text"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.whatWeOffer.text}
          </Typography>

          <List className="what-we-offer-bullets">
            {arVrPageContent.whatWeOffer.items.map((item, index) => (
              <ListItem key={index} className="what-we-offer-item">
                <Typography
                  data-aos="fade-up"
                  data-aos-duration="1000"
                  className="what-we-offer-item-text"
                >
                  {item}
                </Typography>
              </ListItem>
            ))}
          </List>

          <Typography
            className="what-we-offer-conclusion"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.whatWeOffer.conclusion}
          </Typography>
        </Box>

        <Box className="Creative-Design">
          <Typography
            className="Creative-Design-title"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.creativeDesign.title}
          </Typography>
          <Typography
            className="Creative-Design-text"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            {arVrPageContent.creativeDesign.text}
          </Typography>

          <Box className="Creative-Design-bullets-and-image">
            <Box className="Creative-Design-bullets">
              <Typography
                className="Creative-Design-bullets-title"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                {arVrPageContent.creativeDesign.bulletTitle}
              </Typography>

              <List className="Creative-Design-bullets-list">
                {arVrPageContent.creativeDesign.bullets.map((bullet, index) => (
                  <ListItem
                    key={index}
                    className="Creative-Design-bullets-item"
                  >
                    <Typography
                      className="Creative-Design-bullets-text"
                      data-aos="fade-up"
                      data-aos-duration="1000"
                    >
                      {bullet}
                    </Typography>
                  </ListItem>
                ))}
              </List>
            </Box>

            <Box
              className="Creative-Design-image"
              onClick={handleOpenPopup}
              ref={creativeImageRef}
            >
              <Image
                src={AR_VR_Img2}
                alt="Creative Design"
                data-aos="fade-up"
              />
              <Typography
                className={`metaverse-centre-text${
                  showBlink ? " blink" : ""
                } Creative-Design-image-caption`}
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                {arVrPageContent.creativeDesign.imageCaption}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      <AR_VR_PopUp
        open={isPopupOpen}
        onClose={handleClosePopup}
        image={AR_VR_Img3}
        alt="Your image description"
      />
    </Box>
  );
}

export default AR_VRpage;
