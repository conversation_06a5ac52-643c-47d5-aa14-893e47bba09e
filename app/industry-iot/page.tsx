import "./IndustryIOT.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import {
  industry_iot_img1,
  industry_iot_img2,
  industry_iot_img3,
  industry_iot_img4,
  industry_iot_img5,
} from "@/public/index";
import { IndustryIOTContent } from "@/constant";

const IndustryIOTpage = () => {
  return (
    <Box className="IndustryIOT-page-container">
      <Box className="IndustryIOT-page-content">
        <Box
          className="IndustryIOT-header-image"
          data-aos="fade-up"
          data-aos-duration="500"
        >
          <Image src={industry_iot_img1} alt="Industry-IOT" />
        </Box>

        <Box className="IndustryIOT-content">
          <Box
            className="IndustryIOT-description-container"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            {IndustryIOTContent.mainDescription.map((text, index) => (
              <Typography
                key={index}
                className="IndustryIOT-description"
                data-aos="fade-up"
                data-aos-duration="500"
              >
                {text}
              </Typography>
            ))}
          </Box>

          <Box
            className="IndustryIOT-image2-container"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            <Image src={industry_iot_img2} alt="Industry-IOT" />
          </Box>

          <Typography
            variant="body2"
            className="IndustryIOT-description"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            {IndustryIOTContent.tagline}
          </Typography>

          <Typography
            variant="body1"
            className="IndustryIOT-description-2"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            {IndustryIOTContent.subDescription}
          </Typography>

          <Box className="our-iiot-solutions">
            <Typography
              variant="h2"
              className="our-title"
              data-aos="fade-up"
              data-aos-duration="500"
            >
              {IndustryIOTContent.solutions.title}
            </Typography>

            <Box className="our-iiot-solutions-list">
              {IndustryIOTContent.solutions.items.map((item, index) => (
                <Box key={index} className="our-iiot-solutions-item">
                  <Box className="our-iiot-solutions-item-image">
                    <Image
                      src={
                        [
                          industry_iot_img3,
                          industry_iot_img4,
                          industry_iot_img5,
                        ][index]
                      }
                      alt="Industry-IOT"
                      className="our-iiot-solutions-image"
                    />
                    <Typography
                      variant="h3"
                      className="our-iiot-solutions-item-title"
                      data-aos="fade-up"
                      data-aos-duration="500"
                    >
                      {item.title}
                    </Typography>
                  </Box>
                  <Typography
                    className="our-iiot-solutions-description"
                    data-aos="fade-up"
                    data-aos-duration="500"
                  >
                    {item.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>

          <Box className="why-do-we">
            <Typography
              variant="h2"
              className="why-do-we-title"
              data-aos="fade-up"
              data-aos-duration="500"
            >
              {IndustryIOTContent.whySection.title}
            </Typography>
            <Box className="why-do-we-description">
              {IndustryIOTContent.whySection.description.map((text, index) => (
                <Typography
                  key={index}
                  className="why-do-we-description-text"
                  data-aos="fade-up"
                  data-aos-duration="500"
                >
                  {text}
                </Typography>
              ))}
            </Box>

            <Box className="why-do-we-list">
              <List>
                {IndustryIOTContent.whySection.listItems.map((item, index) => (
                  <ListItem
                    key={index}
                    data-aos="fade-up"
                    data-aos-duration="500"
                  >
                    {item}
                  </ListItem>
                ))}
              </List>
            </Box>
          </Box>

          <Box className="our-services">
            <Typography
              variant="h2"
              className="our-services-title"
              data-aos="fade-up"
              data-aos-duration="500"
            >
              {IndustryIOTContent.services.title}
            </Typography>
            <Box className="our-services-cards">
              <Box sx={{ display: "flex", gap: "10.11px" }}>
                {IndustryIOTContent.services.cards
                  .slice(0, 3)
                  .map((card, index) => (
                    <Box
                      key={index}
                      className="our-services-card"
                      data-aos="fade-up"
                      data-aos-duration="500"
                    >
                      <Typography className="content">{card}</Typography>
                    </Box>
                  ))}
              </Box>
              <Box sx={{ display: "flex", gap: "10.11px" }}>
                {IndustryIOTContent.services.cards
                  .slice(3, 6)
                  .map((card, index) => (
                    <Box
                      key={index}
                      className="our-services-card"
                      data-aos="fade-up"
                      data-aos-duration="500"
                    >
                      <Typography className="content">{card}</Typography>
                    </Box>
                  ))}
              </Box>
            </Box>
            <Box className="our-services-description">
              {IndustryIOTContent.services.footerText.map((text, index) => (
                <Typography
                  key={index}
                  className={
                    index === 0
                      ? "our-services-description-text"
                      : "our-services-description-text-2"
                  }
                  data-aos="fade-up"
                  data-aos-duration="500"
                >
                  {text}
                </Typography>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default IndustryIOTpage;
