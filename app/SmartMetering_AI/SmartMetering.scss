@use '../../styles/variables' as *;

.smart-metering-page-container {
    .smart-metering-page-content {
        .smart-metering-header-image {
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 60px;

            img {
                width: 100%;
                max-width: 100%;
                height: auto;
            }
        }

        .smart-metering-content {
            padding-left: 95px;
            padding-right: 95px;
            margin-bottom: 95px;
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
            color: $text-white;


            .smart-metering-title {
                margin-bottom: 33px;
                font-family: Poppins;
                font-weight: 400;
                font-size: 36px;

                line-height: 30px;
                letter-spacing: 0%;
                background: #8CFFE4;
                background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .smart-metering-description {
                display: flex;
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                flex-direction: column;
                gap: 55px;
                margin-bottom: 18px;
            }
        }
    }
}

.metering-services-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

    gap: 30px;
    margin-bottom: 60px;
    margin-top: 50px;

    .service-card {
        border: 1px solid #34495e;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .service-card-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 22px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: center;
            background: #8CFFE4;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 17px;

        }

        .service-list {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 34px;
            letter-spacing: 0%;
            vertical-align: middle;

            li {
                font-size: 0.95rem;
                line-height: 1.6;
                margin-bottom: 15px;
                color: #e8f4fd;
                padding-left: 0;
                position: relative;
                text-align: left;

                &:before {
                    content: '';
                    position: absolute;
                    left: -15px;
                    top: 8px;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}

.smart-metering-container .main-title {
    font-family: Poppins;
    font-weight: 400;
    font-size: 48px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: center;
    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 20px;

}

.smart-metering-container .subtitle {
    font-family: Poppins;
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0%;
    text-align: justify;
    color: #FFFFFF;
    margin-top: 73px;
    text-align: justify;
    padding-left: 70px;
    padding-right: 70px;


}

.features-list {
    font-family: Poppins;
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #FFFFFF;
    margin-top: 50px;
    padding-left: 70px;

    li {
        margin-bottom: 10px;
        list-style-type: disc;
        color: #8CFFE4;

        &:last-child {
            margin-bottom: 0;
        }
    }



}

.content-grid .section-subtitle {
    font-family: Poppins;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0%;
    color: #FFFFFF;
    margin-top: 120px;
    text-align: center;
}


.service-grid {
    padding-left: 70px;
    padding-right: 70px;

    .service-card-wrapper {
        display: flex;
        gap: 50px;

    }

    .service-card {
        padding-left: 23px;
        padding-right: 11px;
        padding-top: 17px;
        padding-bottom: 30px;
        background-color: #093246;
        grid-template-columns: repeat(auto-fit, minmax(300px, 3fr));

        margin-top: 50px;
        box-shadow: 4px 4px 4px 0px #2499E2;



    }
}

.card-description {


    border-radius: 1px;

    color: #ffffff;
    //margin-top: 26px;





}

.service-card {
    .card-title {
        font-family: Poppins;
        font-weight: 600;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        background: #2499E2;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 26px;


    }
}








.smart-metering-flow .flow-title {
    font-family: Poppins;
    font-weight: 600;
    font-size: 40px;
    line-height: 130px;
    letter-spacing: 0%;
    text-align: center;
    color: #FFFFFF;
    margin-top: 173px;
    margin-bottom: 121px;

}



.smart-metering-flow-image {
    width: 100%;
    max-width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 60px;
    padding-left: 70px;
    padding-right: 70px;

    img {
        width: 100%;
        //max-width: 
        //623px;
        height:
            auto;

    }
}

.flow-description {
    font-family: Poppins;
    font-weight: 400;
    font-size: 20px !important;
    line-height: 30px;
    letter-spacing: 0%;
    text-align: justify;
    vertical-align: middle;
    color: #FFFFFF;
    margin-top: 76px;
    padding-left: 70px;
    padding-right: 70px;
}

.flow-description-2 {
    font-family: Poppins;
    font-weight: 400;
    font-style: italic;
    font-size: 20px !important;
    line-height: 30px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #FFFFFF;
    margin-top: 75px !important;
    padding-left: 70px;
    padding-right: 70px;
    margin-bottom: 80px !important;
}