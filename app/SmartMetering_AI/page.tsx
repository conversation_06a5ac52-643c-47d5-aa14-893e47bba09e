import "./SmartMetering.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import { SmartMetering_IOT } from "@/constant/index";

import { SmartMeteringAI_image, SmartMeteringFlow_image } from "@/public/index"; 

const SmartMeteringPage = () => {
  return (
    <>
      <Box className="smart-metering-page-container">
        <Box className="smart-metering-page-content">
          <Box
            className="smart-metering-header-image"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            <Image src={SmartMeteringAI_image} alt="Industry-IOT" />
          </Box>

          <Box className="smart-metering-content">
            <Box className="smart-metering-description-section">
              {SmartMetering_IOT.mainDescription.map((desc, index) => (
                <Typography
                  key={index}
                  variant="body1"
                  className="smart-metering-description"
                >
                  {desc.text}
                </Typography>
              ))}
            </Box>

            <Box className="metering-services-section">
              {SmartMetering_IOT.services.map((service, index) => (
                <Box key={index} className="service-card">
                  <Typography variant="h3" className="service-card-title">
                    {service.title}
                  </Typography>
                  <List className="service-list">
                    {service.items.map((item, itemIndex) => (
                      <ListItem key={itemIndex}>
                        • {item}
                      </ListItem>
                    ))}
                  </List>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>

      <Box className="smart-metering-container">
        {/* Header Section */}
        <Box className="header-section">
          <Typography variant="h1" className="main-title">
            {SmartMetering_IOT.howWeCanHelp.title}
          </Typography>
          <Typography className="subtitle">
            {SmartMetering_IOT.howWeCanHelp.subtitle}
          </Typography>

          <Box className="features-list">
            {SmartMetering_IOT.howWeCanHelp.features.map((feature, index) => (
              <Typography key={index}>
                • {feature}
              </Typography>
            ))}
          </Box>
        </Box>

        {/* Main Content Grid */}
        <Box className="content-grid">
          <Typography className="section-subtitle">
            {SmartMetering_IOT.keyFeatures.subtitle}
          </Typography>
         
          <Box className="service-grid">
            <Box className="service-card-wrapper">
              {SmartMetering_IOT.keyFeatures.cards.slice(0, 2).map((card, index) => (
                <Box key={index} className="service-card">
                  <Typography variant="h3" className="card-title">
                    {card.title}
                  </Typography>
                  <Typography className="card-description">
                    {card.description}
                  </Typography>
                </Box>
              ))}
            </Box>

            <Box className="service-card-wrapper">  
              {SmartMetering_IOT.keyFeatures.cards.slice(2, 4).map((card, index) => (
                <Box key={index + 2} className="service-card">
                  <Typography variant="h3" className="card-title">
                    {card.title}
                  </Typography>
                  <Typography className="card-description">
                    {card.description}
                  </Typography>
                </Box>
              ))}
            </Box>

            <Box className="service-card-wrapper">  
              {SmartMetering_IOT.keyFeatures.cards.slice(4, 6).map((card, index) => (
                <Box key={index + 4} className="service-card">
                  <Typography variant="h3" className="card-title">
                    {card.title}
                  </Typography>
                  <Typography className="card-description">
                    {card.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>

      <Box className="smart-metering-flow">
        <Typography variant="h1" className="flow-title">
          {SmartMetering_IOT.businessDrivers.title}
        </Typography>
      </Box>
      <Box className="smart-metering-flow-image">
        <Image src={SmartMeteringFlow_image} alt="Smart Metering Flow" />
      </Box>
      {SmartMetering_IOT.businessDrivers.description.map((desc, index) => (
        <Typography key={index} variant="body1" className={`flow-description${index > 0 ? '-2' : ''}`}>
          {desc}
        </Typography>
      ))}
    </>
  );
};

export default SmartMeteringPage;