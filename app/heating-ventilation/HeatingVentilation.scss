.heating-ventilation-page-container {
  .heating-ventilation-page-content {
    .heating-ventilation-header-image {
      width: 100%;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 60px;
      position: relative;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        display: block;
      }

      .banner-text-overlay {
        position: absolute;
        top: 90%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 2;
        padding: 0 20px;
        width: 100%;
        max-width: 90vw;

        .banner-title {
          color: #ffffff;
          font-size: 5rem;
          font-weight: 700;
          text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
          margin: 0;
          letter-spacing: 1px;

          @media (max-width: 1024px) {
            font-size: 3.5rem;
          }

          @media (max-width: 768px) {
            font-size: 2.5rem;
          }

          @media (max-width: 480px) {
            font-size: 2rem;
          }
        }
      }
    }

    @media (max-width: 768px) {
      .heating-ventilation-header-image {
        margin-bottom: 40px;

        img {
          border-radius: 6px;
        }
      }
    }
  }
  .heating-ventilation-content {
    margin: 80px 70px;
    .heating-ventilation-description {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
      margin-bottom: 40px;
    }
  }
  .heating-ventilation-title {
    font-family: Poppins;
    font-weight: 400;
    font-size: 30px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: center;
    color: #8cffe4;
    margin-bottom: 35px;
  }
  .heating-ventilation-container {
    margin: 0 139px 80px;
  }

  .heating-ventilation-image {
    margin: 57px 70px 70px 70px;
  }
}
