import "./HeatingVentilation.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { HeatingContentImage, HeatingImage } from "@/public/index";
import HeatingVentilationServices from "@/components/HeatingVentilationServices/HeatingVentilationServices";
import { heatingVentilationServices } from "@/constant/index";

const HeatingVentilationPage = () => {
  return (
    <Box className="heating-ventilation-page-container">
      <Box className="heating-ventilation-page-content">
        <Box
          className="heating-ventilation-header-image"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <Image src={HeatingImage} alt="Heating & Ventilation" />
          <Box className="banner-text-overlay">
            <Typography className="banner-title">
              Heating & Ventilation
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box className="heating-ventilation-content">
        <Typography className="heating-ventilation-description">
          Climate change requires immediate attention as the world starts
          recovering from pandemic situations. A building's energy usage depends
          heavily on its Heating Ventilation and Air Conditioning (HVAC) system
          so smarter sustainable methods of indoor climate control become
          essential for building owners. Aadvik TekLabs collaborates with HVAC
          producers and industrial machine developers to develop all weather
          responsive system with advanced communication technologies with energy
          efficiency.
        </Typography>
        <Typography className="heating-ventilation-description">
          Aadvik TekLabs has a team of expert engineers specializing in power
          electronics domain, software & hardware development, including
          compliance, safety, user experience, and integration with broader
          energy systems and developing custom IT solutions tailored to the
          unique needs of diverse industries like -
        </Typography>
      </Box>
      <Typography className="heating-ventilation-title">
        Key Product Categories We Support
      </Typography>
      <Box className="heating-ventilation-container">
        <HeatingVentilationServices services={heatingVentilationServices} />
      </Box>

      <Box className="heating-ventilation-content">
        <Typography className="heating-ventilation-description">
          Whether it's a compact residential split AC or a large-scale
          commercial HVAC system, we equip our partners with cutting-edge tools
          and technologies to provide forward-thinking HVAC solutions. Our
          dedicated engineering team focuses on delivering systems that are more
          responsive, connected, and environmentally responsible—ensuring
          optimal indoor comfort while significantly reducing energy
          consumption.
        </Typography>
      </Box>

      <Box className="heating-ventilation-image">
        <Image src={HeatingContentImage} alt="Heating & Ventilation" />
      </Box>
    </Box>
  );
};

export default HeatingVentilationPage;
