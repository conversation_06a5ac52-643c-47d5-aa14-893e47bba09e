import "./EvChargingStationPage.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import {
  ev_charging_station_img1,
  ev_charging_station_img2,
} from "@/public/index";
import OfferingPairs from "@/components/OfferingPairs/OfferingPairs";
import { EvChargingStationOurExpertise } from "@/constant/index";

const EvChargingStationPage = () => {
  return (
    <Box className="ev-charging-station-page-container">
      <Box className="ev-charging-station-page-content">
        <Box
          className="ev-charging-station-header-image"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <Image src={ev_charging_station_img1} alt="EV Charging Station" />
        </Box>

        <Box className="ev-charging-station-content">
          <Typography
            variant="h2"
            className="ev-charging-station-title"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            Electric Vehicle & Charging Stations
          </Typography>
          <Box
            className="ev-charging-station-description-container"
            data-aos="fade-up"
            data-aos-duration="1000"
          >
            <Typography
              variant="body1"
              className="ev-charging-station-description"
            >
              Aadvik TekLabs brings deep expertise in building intelligent,
              reliable, and future-ready embedded solutions for the evolving
              automotive landscape. Our team works closely with EV manufacturers
              to deliver high-performance embedded systems products that meet
              the industry’s growing demand for safety, efficiency, and seamless
              user experience.
            </Typography>
            <Typography
              variant="body1"
              className="ev-charging-station-description"
            >
              Our embedded platforms are designed to support a range of electric
              vehicle functions, including safety systems, performance tuning,
              human-machine interface (HMI), communication networks, powertrain
              control, and driver comfort technologies.Aadvik Teklabs is a key
              player in the EVSE domain, its AIoT expertise positions it to make
              unique contributions to the design and development of electric
              vehicle charging infrastructure. We work through strategic
              collaborations and engagement with industry initiatives to provide
              valuable contribution in India's evolving EV ecosystem.
            </Typography>
          </Box>

          <Box className="ev-charging-station-why-us-container">
            <Typography
              variant="h3"
              className="ev-charging-station-why-us-title"
            >
              Why EVSE system design need niche technical skills
            </Typography>

            <Typography
              variant="body1"
              className="ev-charging-station-why-us-description"
            >
              Designing an Electric Vehicle Supply Equipment (EVSE) system
              demands specialized technical expertise due to the complex
              integration of electrical system design, power electronics, secure
              and robust communication protocols, and stringent safety
              standards.
            </Typography>

            <Box className="ev-charging-station-why-us-list-container">
              <List className="ev-charging-station-why-us-list">
                <ListItem className="ev-charging-station-why-us-list-item">
                  Proven Knowledge of end to end smart charging infrastructure
                </ListItem>
                <ListItem className="ev-charging-station-why-us-list-item">
                  Blending embedded system designing
                </ListItem>
                <ListItem className="ev-charging-station-why-us-list-item">
                  Hand-on experience in Power System Design
                </ListItem>
              </List>

              <List className="ev-charging-station-why-us-list">
                <ListItem className="ev-charging-station-why-us-list-item">
                  Electrical System design & wire harnessing
                </ListItem>
                <ListItem className="ev-charging-station-why-us-list-item">
                  Knowledge of EVSE Communication protocols
                </ListItem>
                <ListItem className="ev-charging-station-why-us-list-item">
                  Proven experience in web technologies and backend integration.
                </ListItem>
              </List>
            </Box>

            <Typography
              variant="body1"
              className="ev-charging-station-why-us-description"
            >
              Aadvik TekLabs brings deep domain expertise across this technology
              mix and offer a unique value proposition to our customers that is
              essential to building a capable and proven EVSE solutions for
              EVSE..
            </Typography>
          </Box>

          <Box className="our-expertise-container">
            <Typography variant="h3" className="our-expertise-title">
              Our Expertise
            </Typography>

            <Typography variant="body1" className="our-expertise-description">
              Aadvik Labs team comprises expert engineers specializing in power
              electronics domain, software & hardware development, including
              compliance, safety, user experience, and integration with broader
              energy systems and developing custom IT solutions tailored to the
              unique needs of diverse industries like -
            </Typography>

            <OfferingPairs
              offerings={EvChargingStationOurExpertise}
              className="our-expertise-list"
            />
          </Box>

          <Box className="ev-charging-station-ocpp-container">
            <Typography className="ev-charging-station-ocpp-title" variant="h3">
              OCPP and EV Charging Station
            </Typography>
            <Typography
              className="ev-charging-station-ocpp-description"
              variant="body1"
            >
              The Open Charge Point Protocol (OCPP) is a widely adopted
              open-source communication standard that enables seamless
              interaction between electric vehicle charging stations and central
              management systems. Our engineers plays a key role in ensuring
              interoperability, remote monitoring, smart load management, and
              secure data exchange across various EV charging networks. For
              businesses and operators, OCPP brings flexibility, scalability,
              and vendor independence making it a crucial element in building a
              future-ready, cost-effective EV charging ecosystem.
            </Typography>
            <Typography
              className="ev-charging-station-ocpp-description"
              variant="body1"
            >
              Aadvik TekLabs empowers the future of electric mobility by
              offering advanced EV charging solutions built around the OCPP 2.0
              Subset 1 protocol. OCPP is used to connect electric vehicle (EV)
              chargers and software backend systems using bilateral
              communication. Key expertise requires to ensure reliable
              communication and seamless compatibility across different EV
              charging systems, making it easier for operators to manage their
              networks efficiently. We can support for -
            </Typography>

            <List className="ev-charging-station-ocpp-list">
              <ListItem className="ev-charging-station-ocpp-list-item">
                Implementing Charging Protocols OCPP-x 1.6 and OCPP-2.0.1
              </ListItem>
              <ListItem className="ev-charging-station-ocpp-list-item">
                Seamless Integration with back-end cloud based backbone –
                Enhancing the system operational capabilities
              </ListItem>
              <ListItem className="ev-charging-station-ocpp-list-item">
                Fault recognizance and reporting to backend system in real time
                basis.
              </ListItem>
              <ListItem className="ev-charging-station-ocpp-list-item">
                System Remote Accessibility & Predictive Maintenance for
                real-time data analytics and proactive maintenance.
              </ListItem>
              <ListItem className="ev-charging-station-ocpp-list-item">
                Dynamic Load Balancing – Assisting in balancing heating/cooling
                demand for optimized energy consumption.
              </ListItem>
            </List>

            <Box className="ev-charging-station-ocpp-image-container">
              <Image src={ev_charging_station_img2} alt="EV Charging Station" />
              <Box className="image-caption">
                <Typography variant="body2">
                  OCPP Compliance EV charge point using AWS IoT Core <br /> (A
                  typical Point of view)
                </Typography>
              </Box>
            </Box>

            <Box className="ev-charging-station-iot-integration-container">
              <Typography variant="body2" className="iot-integration-title">
                EVSE Integration with IoT Backend
              </Typography>
              <Typography
                variant="body1"
                className="iot-integration-description"
              >
                Integrating Electric Vehicles (EVs) with IoT backends unlocks a
                wide array of advanced functionalities, such as smart energy
                management, real-time monitoring, and predictive maintenance.
                This connectivity forms the backbone of efficient EV fleet
                operations, enabling optimized charging schedules, enhanced
                performance tracking, and support for emerging technologies like
                Vehicle-to-Grid (V2G). At its core, the integration centers on
                continuous data collection and analysis—from both the vehicle
                and its environment—which empowers proactive maintenance
                strategies and significantly enhances the overall user
                experience.
              </Typography>

              <Typography
                variant="body1"
                className="iot-integration-caption-description"
              >
                In the expanding era of green engineering, collaboration is key
                to <br /> achieving sustainability and maximizing performance.
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default EvChargingStationPage;
