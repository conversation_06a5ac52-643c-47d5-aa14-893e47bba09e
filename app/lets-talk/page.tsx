"use client";

import React, { useState } from "react";
import "./LetsTalk.scss";
import { Box, Divider, Typography } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import PhoneIcon from "@mui/icons-material/Phone";
import { Mail } from "@mui/icons-material";

const LetsTalk: React.FC = () => {
  // State to track the active tab
  const [activeTab, setActiveTab] = useState("Candidate");
  const [messageLength, setMessageLength] = useState(0);

  // Function to handle tab click
  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  // Function to handle message change
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageLength(e.target.value.length);
  };

  return (
    <Box className="get-in-touch-section">
      <Box className="get-in-touch-header">
        <Typography className="get-in-touch-title">
          Get in Touch With Us!
        </Typography>
      </Box>

      <Box className="contact-form-card">
        <Typography className="contact-us-title">Contact Us</Typography>

        <Box className="category-buttons">
          <button
            className={`category-button ${
              activeTab === "Candidate" ? "active" : ""
            }`}
            onClick={() => handleTabClick("Candidate")}
          >
            Candidate
          </button>
          <button
            className={`category-button ${
              activeTab === "Aadvik Alumni" ? "active" : ""
            }`}
            onClick={() => handleTabClick("Aadvik Alumni")}
          >
            Aadvik Alumni
          </button>
          <button
            className={`category-button ${
              activeTab === "Customer" ? "active" : ""
            }`}
            onClick={() => handleTabClick("Customer")}
          >
            Customer
          </button>
          <button
            className={`category-button ${
              activeTab === "Supplier" ? "active" : ""
            }`}
            onClick={() => handleTabClick("Supplier")}
          >
            Supplier
          </button>
        </Box>
        <Divider className="divider" />
        <form className="contact-form">
          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="firstName">*First Name</label>
              <input type="text" id="firstName" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="lastName">*Last Name</label>
              <input type="text" id="lastName" placeholder="" />
            </Box>
          </Box>

          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="company">*Company</label>
              <input type="text" id="company" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="industry">*Industry</label>
              <input type="text" id="industry" placeholder="" />
            </Box>
          </Box>

          <Box className="form-row">
            <Box className="form-group">
              <label htmlFor="phoneNo">Phone No.</label>
              <input type="tel" id="phoneNo" placeholder="" />
            </Box>
            <Box className="form-group">
              <label htmlFor="position">Position</label>
              <input type="text" id="position" placeholder="" />
            </Box>
          </Box>

          <Box className="form-group-message-group">
            <label htmlFor="message">*Message</label>
            <textarea
              id="message"
              placeholder=""
              rows={6}
              maxLength={500}
              onChange={handleMessageChange}
            ></textarea>
            <span className="char-count">{messageLength}/500</span>
          </Box>

          <Box className="checkbox-group">
            <input type="checkbox" id="termsAgree" />
            <label htmlFor="termsAgree">
              *I have read and agreed with <a href="#">Terms of use</a>,{" "}
              <a href="#">Privacy Policy</a> and <a href="#">Cookie Policy</a>.
            </label>
          </Box>

          <Box className="checkbox-group">
            <input type="checkbox" id="newsletterAgree" />
            <label htmlFor="newsletterAgree">
              *Yes, please keep me updated on Aadvik news letter, events, offers
              of services and marketing activity by post, email, sms, MMS,
              phone, social media, push notifications in Apps and other means. I
              understand that I may opt out at any time.
            </label>
          </Box>

          <button type="submit" className="submit-message-button">
            Submit
          </button>
        </form>
      </Box>

      <Box className="contact-info-section">
        <Box className="contact-info-card">
          <Box className="location-header">
            <LocationOnIcon className="icon-placeholder" />
            <Typography variant="h3">India</Typography>
          </Box>
          <Typography className="address-line">#SF04 Mithra Enclave, Doddakalashandra</Typography>
          <Typography>Bangalore 560062</Typography>
          <Box className="phone-number">
            <PhoneIcon className="icon-placeholder" />
            <Typography className="phone-number-text">Tel +91-8077052301</Typography>
          </Box>
          <Box className="email-container">
            <Mail className="icon-placeholder" />
             <a href="mailto:<EMAIL>"><EMAIL></a>
          </Box>
        </Box>
        <Box className="contact-info-card">
          <Box className="location-header">
            <LocationOnIcon className="icon-placeholder" />
            <Typography variant="h3">Canada</Typography>
          </Box>
          <Typography className="address-line">No 33 Kilkarrin Rd, Brampton</Typography>
          <Typography>Ontario L7A4C6</Typography>
          <Box className="phone-number">
            <PhoneIcon className="icon-placeholder" />
            <Typography className="phone-number-text">Tel +91-8077052301</Typography>
          </Box>
          <Box className="email-container">
          <Mail className="icon-placeholder"/>
          <a href="mailto:<EMAIL>"><EMAIL></a>
          </Box>
        </Box>
      </Box>

    </Box>
  );
};

export default LetsTalk;
