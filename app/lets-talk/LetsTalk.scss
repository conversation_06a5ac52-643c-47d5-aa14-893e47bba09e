.get-in-touch-section {
  min-height: 100vh;
  // background: linear-gradient(135deg, #093246 0%, #0a2a3a 50%, #0d1f2a 100%);
  position: relative;
  overflow: hidden;

  // Dotted pattern background
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
  }

  .get-in-touch-header {
    top: 0;
    left: 0;
    right: 200;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding: 40px 0;
    // padding: 65px 223px 59px 468px;
    margin: 57px 0 58px 314px;
    z-index: 2;


    .get-in-touch-title {
      font-family: Ruluko !important;
      font-weight: 400;
      font-size: 80px;
      line-height: 70px;
      letter-spacing: 0%;
      color: #ffffff;
      text-align: center;

      

      @media (max-width: 768px) {
        font-size: 48px;
        line-height: 52px;
      }

      @media (max-width: 480px) {
        font-size: 36px;
        line-height: 40px;
      }
    }
  }

  .main-content {
    display: flex;
    // min-height: 100vh;
    position: relative;
    z-index: 1;
    // padding-top: 140px;

    @media (max-width: 1024px) {
      flex-direction: column;
      padding-top: 120px;
    }
  }

  .contact-info-section {
    flex: 1;
    padding: 139px 0px 0px 103px;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    gap: 180px;

    @media (max-width: 1024px) {
      padding: 40px;
      gap: 60px;
    }

    @media (max-width: 768px) {
      padding: 30px 20px;
      gap: 40px;
    }

    .contact-info-card {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      color: #ffffff;
      font-family: Poppins;

      .location-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .icon-placeholder {
          font-size: 28px;
          color: #46aed7;
          margin-right: 30px;
        }

        h3 {
          font-family: Poppins;
          font-weight: 400;
          font-size: 40px;
          margin: 0;
          color: #ffffff;
          letter-spacing: 0.5px;

          @media (max-width: 768px) {
            font-size: 32px;
          }
        }
      }

      .address-line {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        color: #ffffff;
        margin-left: 60px;
      }

      .phone-number {
        display: flex;
        align-items: center;
        margin: 20px 0 15px 0;

        .icon-placeholder {
          font-size: 22px;
          color: #46aed7;
          margin-right: 30px;
        }

        .phone-number-text {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          color: #ffffff;
        }
      }

      .email-container {
        display: flex;
        align-items: center;

        .icon-placeholder {
          font-size: 22px;
          color: #46aed7;
          margin-right: 30px;
        }

        a {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          color: #ffffff;
          text-decoration: underline;

          &:hover {
            color: #46aed7;
          }
        }
      }
    }
  }

  .contact-form-card {
    border-radius: 20px;
    flex: 1;
    background-color: rgba(217, 217, 217, 0.95);
    padding: 40px;
    margin: 40px 70px 40px 0;
    position: relative;
    z-index: 2;
    // backdrop-filter: blur(10px);

    // @media (max-width: 1024px) {
    //   margin: 20px;
    // }

    // @media (max-width: 768px) {
    //   margin: 20px 10px;
    //   padding: 30px 20px;
    // }

    .contact-us-title {
      font-family: Poppins;
      font-weight: 400;
      font-size: 40px;
      line-height: 30px;
      letter-spacing: 0%;
      background: #000000;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: left;
      margin-bottom: 30px;
      margin-left: 20px;

      @media (max-width: 768px) {
        font-size: 32px;
        line-height: 38px;
      }

      @media (max-width: 480px) {
        font-size: 28px;
        line-height: 34px;
      }
    }

    .category-buttons {
      display: flex;
      gap: 6.35px;
      margin-bottom: 44px;

      .category-button {
        flex: 1;
        background-color: #ffffff;
        border: 1px solid #ffffff;
        padding: 9px 0;
        font-family: Poppins;
        font-size: 20px;
        border-radius: 10px;
        box-shadow: 4px 4px 4px 0px #00000040;
        font-weight: 400;
        color: #021F2E;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        text-align: center;

        &.active {
          background-color: #d77d46;
          color: #000000;
          border-color: #d77d46;
        }

        @media (max-width: 480px) {
          font-size: 12px;
          padding: 8px 0;
        }
      }
    }
    .divider {
      border: none;
      border-top: 1px solid #000000;
      margin: 0 0 30px 0;
    }
    .contact-form {
      .form-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 25px;
        gap: 20px;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 20px;
        }

        .form-group {
          flex: 1;
          display: flex;
          flex-direction: column;

          label {
            font-family: Poppins !important;
            font-weight: 300;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
            color: #000000;
            margin-bottom: 5px;
          }

          input[type="text"],
          input[type="email"],
          input[type="tel"] {
            background-color: #e9e9e9;
            border: 1px solid #8c8a8a;
            // border-radius: 6px;
            padding: 12px 15px;
            font-family: Poppins;
            font-size: 14px;
            color: #333;
            width: 100%;
            box-sizing: border-box;
            transition: all 0.3s ease;

            &:focus {
              outline: none;
              border-color: #d77d46;
              box-shadow: 0 0 0 2px rgba(215, 125, 70, 0.1);
            }

            &::placeholder {
              color: #999;
            }
          }
        }
      }

      .form-group-message-group {
        margin-bottom: 25px;

        label {
          font-family: Poppins !important;
          font-weight: 300;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #000000;
          margin-bottom: 8px;
          display: block;
        }

        textarea {
          background-color: #e9e9e9;
          border: 1px solid #8c8a8a;
          padding: 15px;
          font-family: Poppins;
          font-size: 14px;
          color: #333;
          width: 100%;
          box-sizing: border-box;
          resize: vertical;
          min-height: 286px;
          transition: all 0.3s ease;

          &:focus {
            outline: none;
            border-color: #d77d46;
            box-shadow: 0 0 0 2px rgba(215, 125, 70, 0.1);
          }

          &::placeholder {
            color: #999;
          }
        }

        .char-count {
          display: block;
          text-align: right;
          font-size: 12px;
          color: #666;
          margin-top: 5px;
        }
      }

      .checkbox-group {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        gap: 24px;

        input[type="checkbox"] {
          margin-top: 2px;
          width: 30px;
          height: 35px;
          cursor: pointer;
          accent-color: #d77d46;
          flex-shrink: 0;
        }

        label {
          font-family: Poppins;
          font-size: 20px;
          color: #000000;
          line-height: 1.4;
          flex: 1;

          a {
            color: #2499e2;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .submit-message-button {
        background-color: #d77d46;
        color: #ffffff;
        border: none;
        border-radius: 25px;
        padding: 14px 60px;
        font-family: Poppins;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        display: block;
        margin: 40px auto 0 auto;
        transition: all 0.3s ease;
        min-width: 140px;

        &:hover {
          background-color: #c26b3d;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(215, 125, 70, 0.3);
        }

        @media (max-width: 480px) {
          font-size: 14px;
          padding: 12px 40px;
        }
      }
    }
  }

}
