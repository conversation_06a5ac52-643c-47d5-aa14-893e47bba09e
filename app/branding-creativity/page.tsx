import "./BrandingCreativityPage.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import {
  BrandingPage_img1,
  BrandingPage_img2,
  BrandingPage_img3,
} from "@/public/index";
import { brandingCreativityContent } from "@/constant/index";

const BrandingCreativityPage = () => {
  return (
    <Box className="branding-page-container">
      <Box className="branding-page-content">
        <Box className="branding-header-image" data-aos="fade-down">
          <Image src={BrandingPage_img1} alt="BrandingPage Image" />
        </Box>

        <Box className="branding-content">
          <Box
            className="branding-description-container"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <Typography variant="body1" className="branding-description">
              Effective digital branding helps companies build strong
              relationships with their customers, turning casual browsers into
              loyal customers. Leveraging digital ads and targeted online
              content can significantly enhance businesses’ visibility and
              reputation. This strategic visibility is crucial for attracting
              new customers and maintaining ongoing engagement with existing
              ones.
            </Typography>
            <Typography variant="body1" className="branding-description">
              Consistent and engaging digital branding helps businesses to build
              trust and credibility, a key foundations for long-term growth and
              sustainability in today’s digital-first world.
            </Typography>
          </Box>

          <Box
            className="what-we-serve-container"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <Typography variant="h2" className="branding-title">
              {brandingCreativityContent.whatWeServe.title}
            </Typography>
            <List className="what-we-serve-list">
              {brandingCreativityContent.whatWeServe.items.map(
                (item, index) => (
                  <ListItem
                    key={index}
                    className="what-we-serve-item"
                    data-aos="fade-right"
                    data-aos-delay={300 + index * 100}
                  >
                    {item}
                  </ListItem>
                )
              )}
            </List>
          </Box>

          <Box
            className="product-design-image"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <div data-aos="zoom-in" data-aos-delay="500">
              <Image src={BrandingPage_img2} alt="Branding Page Image 2" />
            </div>

            <Typography
              className="image-title"
              data-aos="fade-up"
              data-aos-delay="600"
            >
              {brandingCreativityContent.productDesign.imageTitle}
            </Typography>

            <div data-aos="zoom-in" data-aos-delay="700">
              <Image src={BrandingPage_img3} alt="Branding Page Image 3" />
            </div>
          </Box>

          <Box className="case-study" data-aos="fade-up" data-aos-delay="200">
            <Typography variant="h2" className="case-study-title">
              {brandingCreativityContent.caseStudy.title}
            </Typography>
            <Box className="case-study-images">
              {brandingCreativityContent.caseStudy.items.map((item, index) => (
                <Box
                  key={index}
                  className="case-study-image-item"
                  data-aos="fade-up"
                  data-aos-delay={900 + index * 100}
                >
                  <Image src={item.image} alt="Web Technologies" />
                  <Typography className="image-title">{item.title}</Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default BrandingCreativityPage;
