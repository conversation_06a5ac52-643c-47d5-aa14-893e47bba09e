import HeroSection from "@/components/heroSection/heroSection";
import { Box, Divider } from "@mui/material";
import "@/app/page.scss";
import WeBuildAccordion from "@/components/weBuildSection/weBuildSection";
import IndustriesWeServe from "@/components/IndustriesWeServe/IndustriesWeServe";
import WhyWeSection from "@/components/whyWeSection/whyWeSection";
import HowWeWork from "@/components/HowWeWork/HowWeWork";
import OurValues from "@/components/OurValues/OurValues";
import LetsTalk from "@/components/LetsTalk/LetsTalk";
import WhyUs from "@/components/WhyUs/WhyUs";

export default function Home() {
  const accordionItems = [
    "Embedded Design & Development Services",
    "Cloud And Mobility Solutions",
    "Wireless Connectivity Enablement",
    "Technology Consulting",
    "Regulatory & Certification Support",
    "3D Model Creation",
    "UI-UX Designing",
    "AR/VR Based Solutions",
    "Mobile App Based Solutions",
    "Web & Cloud Development",
    "AI & Machine Learning",
  ];

  return (
    <Box className="home-page-container">
      <Box className="home-page-content">
        <div data-aos="fade-up">
          <HeroSection />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <WeBuildAccordion items={accordionItems} />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <IndustriesWeServe />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <WhyWeSection />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <HowWeWork />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <OurValues />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <WhyUs />
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <LetsTalk />
        </div>
      </Box>
    </Box>
  );
}
