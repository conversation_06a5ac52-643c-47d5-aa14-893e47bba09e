.iot-page-container {
  .iot-page-content {
    .iot-header-image {
      width: 100%;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 110px;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
      }
    }

    .iot-content {
      padding-left: 95px;
      padding-right: 95px;
      margin-bottom: 95px;

      .iot-content-description-header {
        font-family: Poppins;
        font-weight: 400;
        font-size: 80px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: center;
        color: #8cffe4b2;
        margin-bottom: 50px;
      }

      .iot-title {
        margin-bottom: 33px;
        font-family: Poppins;
        font-weight: 400;
        font-size: 36px;
        letter-spacing: 0%;
        background: #8cffe4;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .iot-description-container {
        display: flex;
        flex-direction: column;
        gap: 55px;
        margin-bottom: 118px;

        .iot-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
        }
      }

      .iot-accordion-container {
        margin-bottom: 70px;

        .iot-title {
          font-family: Poppins;
          font-weight: 300;
          font-size: 50px;

          letter-spacing: 0%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 40px;
        }
      }

      .worked-on-platforms {
        width: 100%;
        margin-bottom: 65px;

        .worked-on-platforms-title {
          font-family: Ruluko !important;
          font-weight: 400;
          font-size: 45px;
          line-height: 30px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          margin-bottom: 50px;
          text-align: center;
        }

        .worked-on-platforms-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          margin-bottom: 29px;
        }

        .platforms-container {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 100%;
          }
        }
      }
    }

    .product-design-image {
      padding: 70px;
      .image-title {
        font-family: Poppins;
        font-weight: 300;
        font-size: 34px;

        letter-spacing: 0%;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 50px;
      }

      .image-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        margin-bottom: 80px;
      }

      .image-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        img {
          margin-bottom: 28px;
          width: 100%;
        }
      }

      .image-caption {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        margin-bottom: 70px;
      }

      .service-list-container {
        .service-list-title {
          font-family: Poppins;
          font-weight: 600;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          color: #ffffff;
          margin-bottom: 50px;
        }

        .service-list {
          .service-item {
            position: relative;
            padding-left: 20px;
            align-items: flex-start;

            &::before {
              content: "•";
              position: absolute;
              left: 0;
              color: #ffffff;
              font-size: 20px;
            }

            .service-item-title {
              font-family: Poppins;
              font-weight: 600;
              font-size: 20px;

              line-height: 30px;
              letter-spacing: 0%;
              vertical-align: middle;
              min-width: fit-content;
              color: #ffffff;
              margin-right: 10px;
            }

            .service-item-description {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;

              line-height: 30px;
              letter-spacing: 0%;
              vertical-align: middle;
              color: #ffffff;
              margin-bottom: 43px;
            }
          }
        }
      }

      .image-container-4 {
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          margin-bottom: 80px;
        }

        .image-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          margin-bottom: 73px;
        }

        .image-caption {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: #ffffff;
        }
      }
    }

    .iot-communication-network-stack-container {
      padding: 70px 70px 144px;
      margin-top: 144px;
      background: #093246;
      .iot-communication-network-stack {
        width: 100%;
        margin-bottom: 80px;

        .iot-communication-title {
          font-family: Ruluko !important;
          font-weight: 400;
          font-size: 45px;
          line-height: 50px;
          letter-spacing: 0%;
          text-align: center;
          color: #ffffff;
          margin-bottom: 50px;
        }

        .iot-communication-content {
          display: flex;
          gap: 60px;
          align-items: flex-start;

          .iot-communication-left {
            flex: 1;

            .iot-communication-description {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;
              line-height: 30px;
              letter-spacing: 0%;
              text-align: justify;
              color: #ffffff;
              margin-bottom: 40px;
            }
          }

          .iot-communication-right {
            flex: 0 0 45%;

            .iot-communication-image-container {
              width: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              border: 1px solid #34495e;
              border-radius: 12px;
              padding: 20px;
              background: rgba(9, 50, 70, 0.3);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

              .iot-communication-image {
                width: 100%;
                height: auto;
                border-radius: 8px;
              }
            }
          }
        }

        .iot-communication-content-image {
          img {
            width: 100%;
            height: auto;
          }
        }
      }
      .iot-grid {
        margin-bottom: 63px;

        .iot-grid-title {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          margin-bottom: 63px;
        }

        .iot-grid-cards {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 30px;
          justify-items: center;
          align-items: start;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .iot-page-container {
    .iot-page-content {
      .iot-content {
        padding-left: 50px;
        padding-right: 50px;

        .iot-communication-network-stack {
          .iot-communication-content {
            flex-direction: column;
            gap: 40px;

            .iot-communication-right {
              flex: 1;
            }
          }
        }

        .iot-grid {
          .iot-grid-cards {
            grid-template-columns: 1fr;
            gap: 25px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .iot-page-container {
    .iot-page-content {
      .iot-content {
        padding-left: 20px;
        padding-right: 20px;

        .iot-communication-network-stack {
          .iot-communication-title {
            font-size: 32px;
            line-height: 40px;
          }

          .iot-communication-content {
            .iot-communication-left {
              .iot-communication-description {
                font-size: 18px;
                line-height: 26px;
              }

              .iot-communication-services {
                .iot-service-card {
                  padding: 20px;

                  .iot-service-title {
                    font-size: 16px;
                    line-height: 22px;
                  }

                  .iot-service-description {
                    font-size: 14px;
                    line-height: 20px;
                  }
                }
              }
            }
          }
        }

        .iot-grid {
          .iot-grid-title {
            font-size: 18px;
            line-height: 26px;
          }

          .iot-grid-cards {
            grid-template-columns: 1fr;
            gap: 20px;
          }
        }
      }
    }
  }
}
