"use client";

import "./IotPage.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { iot_im1, iot_im2, iot_im3, iot_im4, IotImage } from "@/public/index";
import CustomAccordion from "@/components/CustomAccordion/CustomAccordion";
import {
  IotAccordionItems,
  IotCommunicationNetworkStack,
  IotConnectivityServices,
} from "@/constant/index";
import IotServiceCard from "@/components/IotServiceCard/IotServiceCard";
import { useState } from "react";

const IotPage = () => {
  const [expandedPanel, setExpandedPanel] = useState<number | false>(false);

  const handleAccordionChange = (panel: number) => {
    setExpandedPanel(expandedPanel === panel ? false : panel);
  };

  return (
    <Box className="iot-page-container">
      <Box className="iot-page-content">
        <Box className="iot-header-image" data-aos="fade-down">
          <Image src={iot_im1} alt="IOT Image 1" />
        </Box>

        <Box className="iot-content">
          <Typography className="iot-content-description-header">
            Embedded & IoT
          </Typography>
          <Box
            className="iot-description-container"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <Typography variant="body1" className="iot-description">
              Modern electronic systems rely on embedded technologies to create
              secure, connected, and intelligent devices that are designed for
              low-power consumption and efficient operation. Aadvik TekLabs
              exists as a distinctive design service organization which delivers
              full electronic system development solutions starting from
              hardware and progressing to network protocols and certificates.
            </Typography>
            <Typography variant="body1" className="iot-description">
              Our engineers are experienced in the field of hardware and
              software engineering to ensure the system functions efficiently of
              your product and associated application. Our team is specialize in
              delivering comprehensive Embedded Systems services to our clients
              through the entire electronic product development life cycle
              starting from PoC conceptualization stage to the certification
              testing & Compliance. Our expertise spans various industries like
              Sensor Technologies, Smart Homes, Lighting, Consumer Electronics,
              Home Automation, Appliances, HVAC, Industrial Machinery, and
              Wearables.
            </Typography>
          </Box>

          <Box className="iot-accordion-container">
            <Typography variant="h2" className="iot-title">
              The versatility of our offerings are -
            </Typography>{" "}
            {IotAccordionItems.map((item, index) => (
              <CustomAccordion
                key={index}
                index={index}
                label={item.accordionLabel}
                details={item.details}
                defaultExpanded={false}
                expanded={expandedPanel === index}
                onChange={handleAccordionChange}
                color="#AEB8B6"
                fontStyle="italic"
              />
            ))}
          </Box>

          <Box
            className="worked-on-platforms"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <Typography variant="h2" className="worked-on-platforms-title">
              Embedded Platforms We Have Worked On
            </Typography>
            <Typography
              variant="body1"
              className="worked-on-platforms-description"
            >
              With vast experience across diverse platforms, we design and
              deliver customized solutions to meet the unique needs of each
              client. From low-power microcontrollers to powerful processors,
              our expertise allows us to craft innovative solutions that
              optimize performance, ensure seamless integration, and meet
              project constraints. Leveraging both hardware and software
              knowledge, we enhance the efficiency, scalability, and
              functionality of our clients' systems.
            </Typography>

            <Box
              data-aos="zoom-in"
              data-aos-delay="500"
              className="platforms-container"
            >
              <Image src={iot_im2} alt="iot Page Image 2" />
            </Box>
          </Box>
        </Box>
        <Box className="iot-communication-network-stack-container">
          <Box
            className="iot-communication-network-stack"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <Typography variant="h2" className="iot-communication-title">
              {IotCommunicationNetworkStack.title}
            </Typography>

            {/* <Box className="iot-communication-content">
              <Box className="iot-communication-left">
                <Typography
                  variant="body1"
                  className="iot-communication-description"
                >
                  {IotCommunicationNetworkStack.description}
                </Typography>
              </Box>

              <Box className="iot-communication-right">
                <Box className="iot-communication-image-container">
                  <Image
                    src={iot_im3}
                    alt="IoT Communication & Network Stack"
                    className="iot-communication-image"
                    data-aos="zoom-in"
                    data-aos-delay="500"
                  />
                </Box>
              </Box>
            </Box> */}
            <Box className="iot-communication-content-image">
              <Image src={IotImage} alt="IoT Communication & Network Stack" />
            </Box>
          </Box>

          <Box className="iot-grid">
            <Typography className="iot-grid-title">
              IoT-based wireless communication enables seamless connectivity
              between smart devices, improving automation, efficiency, and
              data-driven decision-making across industries. The choice of
              wireless technology depends on factors like range, power
              consumption, and data rate requirements. Our Connectivity Services
              Includes -
            </Typography>

            <Box className="iot-grid-cards">
              {IotConnectivityServices.map((service, index) => (
                <IotServiceCard
                  key={index}
                  title={service.title}
                  description={service.description}
                />
              ))}
            </Box>
          </Box>
        </Box>
        <Box
          className="product-design-image"
          data-aos="fade-up"
          data-aos-delay="400"
        >
          <Box className="image-container-4">
            <Image
              src={iot_im4}
              alt="iot Page Image 4"
              className="iot-image-4"
              data-aos="zoom-in"
              data-aos-delay="600"
            />
            <Typography
              className="image-description"
              data-aos="fade-up"
              data-aos-delay="600"
            >
              With growing era of green engineering market, Aadvik’s team excels
              at developing sustainable and efficient systems to meet your
              requirements with reliable security in place. Whether it is adding
              connectivity module to your Legacy or complete system design, we
              specialize in developing the electronic systems which can be your
              technology building blocks to address your customers needs,
              ensuring both environmental sustainability and optimal
              performance.
            </Typography>

            <Typography className="image-caption">
              Partner with us to navigate the complexities of IoT connectivity
              and security, and to deploy solutions that drive innovation and
              efficiency in your operations.
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default IotPage;
