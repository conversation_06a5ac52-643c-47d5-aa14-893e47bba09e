import "./SmartHome.scss";
import { Box, Divider, Typography } from "@mui/material";
import Image from "next/image";
import { SmartHomeContentImage, SmartHomeGroup, SmartHomeImage } from "@/public/index";

const SmartHomePage = () => {
  return (
    <Box className="smart-home-container">
      <Box className="smart-home-page-content">
        <Box
          className="smart-home-header-image"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <Image src={SmartHomeImage} alt="smart&homes" />
          <Box className="banner-text-overlay">
            <Typography className="banner-title">
              Smart Home & Buildings
            </Typography>
          </Box>
        </Box>
      </Box>

      <Box className="smart-home-subtitle">
        <Typography className="subtitle-one">
          Aadvik Teklabs, a leading engineering service provider in the smart
          home domain, delivers fully customized home automation solutions,
          demonstrating a strong commitment to end-to-end automation and
          security. By driving the development of futuristic living spaces,
          below are key expertise which Aadvik Team has evolved to build a
          proven smart home system -
        </Typography>
        <Typography className="subtitle-two">
          <ul className="bullet-list">
            <li>
              Connect and manage smart devices via industry standard protocols
              like Zigbee, Z-Wave, or Wi-Fi.
            </li>
            <li>
              Proven system design knowledge of Micro system, sensors, and
              actuators for device control.
            </li>
            <li>
              Mobile & Web App Development – Creating user-friendly interfaces
              for control and monitoring.
            </li>
            <li>
              Implementing the encryption, authentication, and data protection
              algorithms to secure the system.
            </li>
            <li>
              Designing intelligent routines and learning systems for adaptive
              home behavior using GenAI.
            </li>
            <li>
              Manage, storage, process the data by leveraging the power of edge
              computing and remote access via cloud platforms.
            </li>
          </ul>
        </Typography>
        <Typography className="subtitle-three">
          Aadvik Teklabs integrates cutting-edge technology with contemporary
          luxury—establishing itself as a key innovator in the smart home
          industry.
        </Typography>
      </Box>

      <Box className="automation-system-design">
        <Typography className="automation-system-design-title">
          Enabling OEMs with Intelligent Home Automation System Design
        </Typography>
        <Typography className="automation-system-design-subtitle">
          The world of Smart Lighting and Home Automation is rapidly evolving
          with demand for efficient and intelligent connected robust and
          trustworthy systems with energy savings. We at AadvikLabs enables the
          intelligence in Legacy products to make them smart and efficient to
          transforms the traditional home into a more secure, user-friendly and
          all-time available by leveraging technology to streamline daily tasks
          and routines.
        </Typography>
        <Typography className="automation-system-design-subtitle-two">
          Aadvik Teklabs has a team of expert engineers specializing in power
          electronics domain, software & hardware development, including
          compliance, safety, user experience, and integration with broader
          energy systems and developing custom IT solutions tailored to the
          unique needs of diverse industries like -
        </Typography>
      </Box>

      <Box className="smart-home-image-container">
        <Image src={SmartHomeGroup} alt="smart&homes" />
      </Box>

      <Box className="how-we-support-section">
        <Typography
          className="how-we-support-title"
          data-aos="fade-up"
          data-aos-duration="3000"
        >
          How We Support ?
        </Typography>

        <Typography
          className="how-we-support-description"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          We collaborate with OEMs to develop high-performance smart home and
          lighting systems that integrate energy efficiency with advanced
          automation features. Our expertise also extends to building
          intelligent home automation solutions that deliver seamless control,
          enhanced security, and optimized energy use—empowering smarter, more
          connected living environments.
        </Typography>

        <Box className="support-services-container">
          <Box className="support-service-column">
            <Typography
              className="support-service-column-title"
              data-aos="fade-up"
              data-aos-duration="1000"
            >
              Luminescence Solutions
            </Typography>
            <Box className="support-service-list">
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Lighting Systems for Appliances
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Smart Street Lighting
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Lighting Controllers, Sensors& Drivers
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Signage Lighting Design
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Horticultural Lighting
              </Typography>
            </Box>
          </Box>

          <Box className="support-service-column">
            <Typography
              className="support-service-column-title"
              data-aos="fade-up"
              data-aos-duration="1000"
            >
              Home Automation System
            </Typography>
            <Box className="support-service-list">
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Voice & Remote Control
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ HVAC Control systems
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Home Security & Access
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Shades, Doors & Gates
              </Typography>
              <Typography
                className="support-service-item"
                data-aos="fade-up"
                data-aos-duration="1000"
              >
                ✓ Alarms & Safety Systems
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      <Divider className="divider" />
      <Box className="support-service-column">
        <Typography className="product-engineering-title">
          Product Engineering Services for Lighting, Home Automation & Consumer
          Electronics
        </Typography>
        <Typography className="product-engineering-description">
          The modern technological development requires innovative solutions for
          lighting control systems combined with automated home devices and
          consumer electronic products. Modern businesses create aware products
          which use minimal energy and match modern living styles.
          Market-leading organizations select professional product engineering
          experts to translate their provisional ideas into operational
          realities in this evolving market environment.
        </Typography>
        <Typography className="product-engineering-description-two">
          Aadvik Teklabs delivers complete product engineering solutions for
          Lighting and Home Automation and consumer electronics products
          according to sector demands. Aadvik Teklabs integrates experts from
          design and engineering and technology fields to create solutions which
          support functionality while creating consumer-brand connection points.
        </Typography>
      </Box>

      <Box className="home-image-container">
        <Image src={SmartHomeContentImage} alt="smart&homes" />
      </Box>
    </Box>
  );
};

export default SmartHomePage;
