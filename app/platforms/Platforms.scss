.platforms-container {
  background-color: #021f2e;
  padding: 53px 27px;
  .platforms-container-title-row {
    margin: 26px 0 46px;

    .platforms-container-title {
      font-family: 'Ephesis', cursive;
      font-weight: 400;
      font-size: 48px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      color: #ffffff;
    }
  }
  .platforms-container-row {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .sync-master {
    margin: 72px 70px 65px 70px;

    .sync-master-header {
      font-family: Poppins;
      font-weight: 600;
      font-size: 36px;
      line-height: 30px;
      letter-spacing: 0%;
      vertical-align: middle;
      color: #ffffff;
      margin-bottom: 65px;
    }
    .sync-master-description-one {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      // leading-trim: Cap height;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
    }
    .sync-master-description-two {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
      margin-top: 50px;
    }
  }
  .management-modules-header {
    margin: 0 70px 0 70px;
    .management-modules-title {
      font-family: Poppins;
      font-weight: 400;
      font-size: 34px;
      line-height: 100%;
      letter-spacing: 0%;
      background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .management-modules-description {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
      margin: 15px 0 50px 0;
    }
    .management-modules-description-two {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
      margin-bottom: 48px;
    }
  }

  .crm-image-container {
    padding: 0 70px 0 70px;
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 70px;

    .crm-image {
      width: 100%;
      max-width: 1900px;
      height: auto;
    }
  }
  .crm-modules-header {
    margin: 0 70px 70px 70px;
    .crm-image-title {
      margin: 53px 0 106px 0;
      font-family: Poppins;
      font-weight: 300;
      font-size: 34px;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      color: #549989;
    }
    .crm-image-description {
      font-family: Poppins;
      font-weight: 400;
      font-style: italic;
      font-size: 30px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      color: #ffffff;
    }
  }
}
