import "./cloud-mobility.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import { cloud_mobility_img1, cloud_mobility_img2 } from "@/public/index";
import { cloudMobilityContent } from "@/constant/index";

const CloudMobilityPage = () => {
  return (
    <Box className="cloud-mobility-page-container">
      <Box className="cloud-mobility-page-content">
        <Box className="cloud-mobility-header-image" data-aos="fade-down">
          <Image src={cloud_mobility_img1} alt="Cloud Mobility Image 1" />
        </Box>

        <Box className="content">
          <Typography
            className="header-description"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            {cloudMobilityContent.header.description}
          </Typography>

          <Box
            className="mobile-app-development"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <Typography variant="h4" className="mobile-app-development-title">
              {cloudMobilityContent.mobileAppDevelopment.title}
            </Typography>
            <Typography className="mobile-app-development-description">
              {cloudMobilityContent.mobileAppDevelopment.description1}
            </Typography>
            <Typography className="mobile-app-development-description">
              {cloudMobilityContent.mobileAppDevelopment.description2}
            </Typography>

            <List className="cloud-mobility-list">
              {cloudMobilityContent.mobileAppDevelopment.capabilities.map(
                (item, index) => (
                  <ListItem key={index} className="cloud-mobility-list-item">
                    {item}
                  </ListItem>
                )
              )}
            </List>
          </Box>

          <Box className="expertise">
            <Typography className="mobile-app-development-title">
              {cloudMobilityContent.expertise.title}
            </Typography>
            <Typography className="mobile-app-development-description">
              {cloudMobilityContent.expertise.description}
            </Typography>

            <List className="mobile-development-list">
              {cloudMobilityContent.expertise.items.map((item, index) => (
                <ListItem key={index} className="mobile-development-list-item">
                  {item}
                </ListItem>
              ))}
            </List>

            <Typography className="mobile-app-development-description">
              {cloudMobilityContent.expertise.benefitsTitle}
            </Typography>

            <List className="mobile-development-list">
              {cloudMobilityContent.expertise.benefits.map((item, index) => (
                <ListItem key={index} className="mobile-development-list-item">
                  {item}
                </ListItem>
              ))}
            </List>
          </Box>
          <Typography className="devOpsConsut-title">
            {cloudMobilityContent.devOpsConsulting.title}
          </Typography>
          <Box className="devops-consulting">
            <Box className="devops-consulting-description-image">
              <Box
                className="devops-consulting-description-wrapper"
                data-aos="fade-up"
                data-aos-delay="100"
              >
                <Typography className="devops-consulting-description">
                  {cloudMobilityContent.devOpsConsulting.description1}
                </Typography>

                <Typography className="devops-consulting-description">
                  {cloudMobilityContent.devOpsConsulting.description2}
                </Typography>
              </Box>
              <Box
                className="devops-consulting-image"
                data-aos="fade-up"
                data-aos-delay="100"
              >
                <Image
                  src={cloud_mobility_img2}
                  alt="DevOps Consulting Image"
                />
              </Box>
            </Box>

            {/* <Typography className="devops-consulting-title-2">
              {cloudMobilityContent.devOpsConsulting.title}
            </Typography> */}
            <List className="devops-consulting-list">
              {cloudMobilityContent.devOpsConsulting.services.map(
                (item, index) => (
                  <ListItem key={index} className="devops-consulting-list-item">
                    {item}
                  </ListItem>
                )
              )}
            </List>
            <Typography className="devops-consulting-description">
              {cloudMobilityContent.devOpsConsulting.benefitsTitle}
            </Typography>

            <List className="devops-consulting-list">
              {cloudMobilityContent.devOpsConsulting.benefits.map(
                (item, index) => (
                  <ListItem key={index} className="devops-consulting-list-item">
                    {item}
                  </ListItem>
                )
              )}
            </List>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default CloudMobilityPage;
