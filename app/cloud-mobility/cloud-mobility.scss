.cloud-mobility-page-container {
  .cloud-mobility-page-content {
    .cloud-mobility-header-image {
      margin-bottom: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;

      img {
        width: 100%;
        height: 100vh;
      }
    }

    .content {
      padding: 80px 70px;

      .header-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        margin-bottom: 32px;
        color: #ffffff;
      }

      .mobile-app-development {
        display: flex;
        flex-direction: column;
        // align-items: center;
        gap: 20px;

        .mobile-app-development-title {
          font-family: Poppins;
          font-weight: 275;
          font-size: 34px;

          letter-spacing: 0%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 0px;
        }

        .mobile-app-development-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #ffffff;
        }

        .cloud-mobility-list {
          width: 100%;
          margin-bottom: 51px;

          .cloud-mobility-list-item {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;

            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #ffffff;
          }

          & .cloud-mobility-list-item::before {
            content: "";
            display: inline-block;
            width: 5px;
            height: 5px;
            background-color: #ffffff;
            border-radius: 50%;
            margin-right: 10px;
          }
        }
      }

      .expertise {
        .mobile-app-development-title {
          font-family: Poppins;
          font-weight: 300;
          font-size: 34px;

          line-height: 40px;
          letter-spacing: 0%;
          background: linear-gradient(180deg, #78ddc8 0%, #41776c 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 47px;
        }

        .mobile-app-development-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          margin-bottom: 50px;
        }

        .mobile-development-list {
          width: 100%;
          margin-bottom: 51px;

          .mobile-development-list-item {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #ffffff;
            display: flex;
            align-items: start;
          }

          & li::before {
            content: "•";
            color: #ffffff;
            font-size: 20px;
            margin-right: 10px;
          }
        }
      }
      .devOpsConsut-title {
        font-family: Poppins;
        font-weight: 275;
        font-size: 34px;
        // line-height: 100%;
        letter-spacing: 0%;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 51px;
      }
      // .devops-consulting-title {
      //     font-family: Poppins;
      //     font-weight: 275;
      //     font-size: 34px;
      //     // text-align: center;
      //     letter-spacing: 0%;
      //     color: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
      //     line-height: 100%;

      //     background-clip: text;
      //     -webkit-text-fill-color: transparent;
      //     margin-bottom: 43px;
      //   }

      .devops-consulting {
        // .devops-consulting-title {
        //   font-family: Poppins;
        //   font-weight: 275;
        //   font-size: 34px;
        //   // text-align: center;
        //   letter-spacing: 0%;
        //   color: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        //   line-height: 100%;

        //   background-clip: text;
        //   -webkit-text-fill-color: transparent;
        //   margin-bottom: 43px;
        // }

        .devops-consulting-description-image {
          display: flex;
          gap: 38px;

          .devops-consulting-description-wrapper {
            max-width: 50%;

            .devops-consulting-description {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;

              line-height: 30px;
              letter-spacing: 0%;
              text-align: justify;
              color: #ffffff;
            }
          }

          .devops-consulting-image {
            width: 100%;

            img {
              width: 100%;
            }
          }
        }

        .devops-consulting-title-2 {
          font-family: Poppins;
          font-weight: 275;
          font-size: 34px;
          letter-spacing: 0%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 41px;
        }

        .devops-consulting-list {
          width: 100%;
          margin-bottom: 70px;

          .devops-consulting-list-item {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #ffffff;
            display: flex;
            align-items: start;
          }

          & li::before {
            content: "•";
            color: #ffffff;
            font-size: 20px;
            margin-right: 10px;
          }
        }

        .devops-consulting-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 24px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          margin-bottom: 50px;
        }
      }
    }
  }
}
