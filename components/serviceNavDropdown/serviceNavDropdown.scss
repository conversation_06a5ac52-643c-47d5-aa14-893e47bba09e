.service-nav-dropdown {
    .service-nav-header {
        display: grid;
        grid-template-columns: repeat(3, 1fr);

        &>div:nth-child(even) .service-card {
            background: #021F2E !important;
        }
    }

    .service-card {
        // OLD STYLING
        // width: 466px;
        // height: 340px;
        padding: 20px 0px 20px 13px;
        min-width: 370px;
        height: 100%;
        max-height: 400px;
        position: relative;
        overflow: hidden;
        border: 1px solid #2499E280;
        background: #093246;
        transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;


        &:hover {
            .icon-wrapper {
                opacity: 1;
                transform: translate(0, 0) scale(1.1);
            }
        }

        .service-header {
            position: relative;
            z-index: 2;
            margin-bottom: 12px;
            transform-origin: center;
            transition: all 0.3s ease-in-out;

            .service-title {

                color: #8CFFE4;
                font-family: Montserrat;
                font-weight: 500;
                // font-size: 22px;
                font-size: 18px;
                line-height: 30px;
                letter-spacing: 0;
                margin-left: 18px;
                cursor: pointer;

            }

            &:hover {
                transform: translateY(-2px);
            }

            &.clicked {
                animation: clickEffect 0.3s ease-in-out;
            }
        }

        .service-items {
            list-style: none;
            padding: 0;
            margin: 0;
            position: relative;
            z-index: 2;

            .service-item {
                font-family: Montserrat;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                letter-spacing: 0%;
                color: #FFFFFF;
                cursor: pointer;


                &:hover {
                    color: #D77D46;
                }

                &::before {
                    content: "^";
                    color: #FFFFFF;
                    margin-right: 8px;
                }
            }
        }

        .icon-wrapper {
            position: absolute;
            bottom: 20px;
            right: 20px;
            opacity: 1;
            transform: translate(5px, 5px);
            transition: all 0.3s ease;
            z-index: 1;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            // background: #093246;
            border-radius: 4px;

            img {
                width: 70%;
                height: 70%;
                object-fit: contain;
                opacity: 0.8;
            }
        }
    }
}

@keyframes clickEffect {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(0.95);
        opacity: 0.8;
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}

@media screen and (min-width: 1200px) and (max-width: 1600px) {}


@media screen and (max-width: 1199px) {}

@media screen and (max-width: 768px) {
    .service-nav-header {
        display: flex !important;
        flex-wrap: wrap;

    }
}