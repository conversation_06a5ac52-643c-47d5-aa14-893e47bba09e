"use client";

import React from "react";
import "./serviceNavDropdown.scss";
import Image from "next/image";
import { Box, Typography } from "@mui/material";
import { useRouter } from "next/navigation";

interface ServiceItem {
  title: string;
  items: string[];
  icon: any;
  route?: string; // Added route property
}

interface ServiceNavDropdownProps {
  data: ServiceItem[];
  onClose?: () => void;
}

const ServiceNavDropdown: React.FC<ServiceNavDropdownProps> = ({
  data,
  onClose,
}) => {
  const router = useRouter();

  const handleServiceClick = (route: string, event: React.MouseEvent) => {
    const element = event.currentTarget as HTMLElement;
    element.classList.add("clicked");

    // Wait for the animation to complete before navigation
    setTimeout(() => {
      router.push(route);
      if (onClose) {
        onClose();
      }
    }, 300); // Match this with animation duration
  };

  return (
    <Box className="service-nav-dropdown">
      <Box className="service-nav-header">
        {data.map((service, index) => (
          <Box key={index}>
            <Box className="service-card">
              <Box
                className="service-header"
                onClick={(e) =>
                  service.route && handleServiceClick(service.route, e)
                }
                style={{ cursor: "pointer" }}
              >
                <Typography variant="h6" className="service-title">
                  {service.title}
                </Typography>
              </Box>
              <ul className="service-items">
                {service.items.map((item, idx) => (
                  <li key={idx} className="service-item">
                    {item}
                  </li>
                ))}
              </ul>
              {service.icon && (
                <Box className="icon-wrapper">
                  <Image
                    src={service.icon}
                    alt={service.title}
                    width={140}
                    height={140}
                  />
                </Box>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ServiceNavDropdown;
