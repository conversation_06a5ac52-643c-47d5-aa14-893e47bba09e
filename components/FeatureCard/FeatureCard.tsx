"use client";

import React from "react";
import "./FeatureCard.scss";
import { Box } from "@mui/material";
import Image from "next/image";
import type { StaticImageData } from "next/image";

interface FeatureCardProps {
  image: string | StaticImageData;
  altText: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ image, altText }) => {
  return (
    <Box className="feature-card">
      <Image
        src={image}
        alt={altText}
        className="feature-card-image"
        objectFit="contain"
      />
    </Box>
  );
};

export default FeatureCard;
