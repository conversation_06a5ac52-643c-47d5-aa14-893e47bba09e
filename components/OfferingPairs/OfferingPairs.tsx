import React from "react";
import { Box, Typography, List, ListItem } from "@mui/material";
import "./OfferingPairs.scss";

interface OfferingItem {
  title: string;
  description?: string;
  bullets?: string[];
}

interface OfferingPairsProps {
  offerings: OfferingItem[];
  className?: string;
}

const OfferingPairs: React.FC<OfferingPairsProps> = ({
  offerings,
  className = "",
}) => {
  const renderContent = (item: OfferingItem) => {
    if (item.bullets && item.bullets.length > 0) {
      return (
        <List className="offering-bullets">
          {item.bullets.map((bullet, index) => (
            <ListItem key={index} className="offering-bullet-item">
              {bullet}
            </ListItem>
          ))}
        </List>
      );
    }
    return (
      <Typography variant="body1" className="offering-description">
        {item.description}
      </Typography>
    );
  };

  const renderPairs = () => {
    const pairs = [];
    for (let i = 0; i < offerings.length; i += 2) {
      pairs.push(
        <Box key={i} className="offering-item-wrapper">
          <Box className="offering-item">
            <Typography variant="h3" className="offering-item-title">
              {offerings[i].title}
            </Typography>
            {renderContent(offerings[i])}
          </Box>

          {offerings[i + 1] && (
            <Box className="offering-item">
              <Typography variant="h3" className="offering-item-title">
                {offerings[i + 1].title}
              </Typography>
              {renderContent(offerings[i + 1])}
            </Box>
          )}
        </Box>
      );
    }
    return pairs;
  };

  return (
    <Box className={`our-offerings-list ${className}`}>{renderPairs()}</Box>
  );
};

export default OfferingPairs;
