// .offerings-list {
//     .offering-item-wrapper {
//         display: flex;
//         gap: 24px;
//         margin-bottom: 24px;

//         .offering-item {
//             flex: 1;
//             padding: 24px;
//             background: rgba(215, 125, 70, 0.05);
//             border-radius: 8px;
//             width: 100%;

//             .offering-item-title {
//                 font-family: Poppins;
//                 font-weight: 500;
//                 font-size: 24px;
//                 line-height: 36px;
//                 color: #8CFFE4;
//                 margin-bottom: 16px;
//             }

//             .offering-description {
//                 font-family: Poppins;
//                 font-weight: 400;
//                 font-size: 16px;
//                 line-height: 24px;
//                 color: #FFFFFF;
//             }
//         }
//     }
// }


.our-offerings-list {
    display: flex;
    flex-direction: column;
    gap: 15px;

    .offering-item-wrapper {
        display: flex;
        gap: 15px;
    }

    .offering-item {
        padding: 38.09px 27.41px 39.58px 34px;
        width: 100%;
        min-height: 280px;
        border: 1px solid #2499E280;
        color: #FFFFFF;

        .offering-item-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 22px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: center;
            background: #8CFFE4;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30.48px;
        }

        .offering-description {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            color: #FFFFFF;
        }

        .offering-bullets {
            padding: 0;
            margin: 0;

            .offering-bullet-item {
                font-family: Poppins;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                letter-spacing: 0%;
                color: #FFFFFF;
                padding: 8px 0;
                display: flex;
                align-items: flex-start;

                &::before {
                    content: "•";
                    color: #FFFFFF;
                    margin-right: 12px;
                    font-size: 20px;
                }
            }
        }
    }
}