import React from "react";
import "@/components/HowWeWork/HowWeWork.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  HowWeWork_Img_1,
  HowWeWork_Img_2,
  SeeOurProjects_icon,
} from "@/public/index";

const HowWeWork: React.FC = () => {
  return (
    <Box className="how-we-work-container">
      <Typography className="how-we-work-title" variant="h5">
        How <span>it Work</span>
      </Typography>

      <Box className="how-we-work-images">
        <Image
          src={HowWeWork_Img_1}
          alt="Why We Section Image"
          // fill
          style={{
            objectFit: "cover",
            borderRadius: "8px",
            // transition: "transform 0.3s ease",
          }}
        />
        <Image
          src={HowWeWork_Img_2}
          alt="Why We Section Image"
          // fill
          style={{
            objectFit: "cover",
            borderRadius: "8px",
            // transition: "transform 0.3s ease",
          }}
        />
      </Box>

      <Box className="how-we-work-text">
        <Typography className="how-we-work-description">
          We believe that great digital products are the result of thoughtful
          strategy, human-centered design, and close collaboration with our
          clients. Our process is designed to deliver innovative, scalable, and
          user-focused solutions while keeping communication transparent and
          expectations aligned every step of the way.
        </Typography>

        <Typography className="how-we-work-description-2">
          Please go through our process document to understand how we make
          successful products and serve our clients.
        </Typography>
      </Box>

      <Box className="see-our-projects">
        <Typography className="see-our-projects-text">
          See our Process
        </Typography>
        <Box className="see-our-projects-button">
          <Image
            src={SeeOurProjects_icon}
            alt="See Our Projects Icon"
            width={82}
            height={93}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default HowWeWork;
