"use client";

import React from "react";
import "@/components/footer/Footer.scss";
import { footerLinks } from "@/constant/index";
import Link from "next/link";
import {
  Box,
  Typography,
  Container,
  Grid,
  List,
  ListItem,
  IconButton,
  TextField,
  Button,
  Stack,
} from "@mui/material";
import { Twitter, LinkedIn, YouTube, Instagram } from "@mui/icons-material";
import Image from "next/image";
import { companyLogo } from "@/public/index";

const Footer: React.FC = () => {
  return (
    <Box component="footer">
      {/* Top Grid Section */}
      <Box className="footer-top-grid">
        <Container className="footer-container">
          <Box className="footer-top-grid-box">
            {Object.entries(footerLinks).map(([title, items]) => (
              <Box key={title} className="footer-grid-item">
                <Typography className="footer-title" variant="h6">
                  {title}
                </Typography>
                <List dense className="footer-list">
                  {items.map((item) => (
                    <ListItem key={item.label} className="footer-list-item">
                      <Link
                        href={item.path}
                        style={{ textDecoration: "none", color: "inherit" }}
                      >
                        <Typography
                          variant="body2"
                          className="footer-list-text"
                        >
                          {item.label}
                        </Typography>
                      </Link>
                    </ListItem>
                  ))}
                </List>
              </Box>
            ))}
          </Box>
        </Container>
      </Box>

      {/* Bottom Section */}
      <Box className="footer-bottom-section">
        <Container className="footer-container">
          <Grid className="footer-bottom-grid">
            {/* Left - Logo and Social */}
            <Box className="footer-left">
              <Image
                src={companyLogo}
                alt="Aadvik Teklabs Logo"
                width={240.25}
                height={83.15}
              />
              <Typography variant="body2" mt={2} className="footer-copyright">
                Aadvik Teklabs Private Limited
              </Typography>
            </Box>

            <Box>
              {/* Middle */}
              <Stack direction="row" spacing={2} mt={2}>
                {[Instagram, Twitter, LinkedIn, YouTube].map((Icon, index) => (
                  <IconButton
                    key={index}
                    sx={{
                      border: "1px solid white",
                      color: "white",
                      width: 60,
                      height: 60,
                    }}
                  >
                    <Icon fontSize="large" />
                  </IconButton>
                ))}
              </Stack>
              <Typography variant="body2" mt={2} className="footer-copyright">
                © 2024. All Rights Reserved.
              </Typography>
            </Box>

            {/* Right - Newsletter and Legal Links */}
            <Box>
              <Box sx={{ textAlign: { xs: "left", md: "right" } }}>
                <Typography
                  variant="body2"
                  sx={{ mb: 1 }}
                  className="footer-newsletter"
                >
                  Subscribe to our organization newsletter
                </Typography>
                <Box
                  display="flex"
                  flexDirection={"column"}
                  justifyContent={{ xs: "flex-start", md: "flex-end" }}
                >
                  <TextField
                    placeholder="Email Address"
                    size="small"
                    sx={{
                      backgroundColor: "white",
                      borderRadius: "20px",
                      "& input": { paddingY: "8px", paddingLeft: 2 },
                      width: "424px",
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "20px",
                        paddingRight: "0",
                        "&:focus-within": {
                          "& > fieldset": {
                            borderColor: "rgba(0, 0, 0, 0.23)",
                            boxShadow: "none",
                          },
                        },
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderRadius: "20px",
                      },
                    }}
                    InputProps={{
                      endAdornment: (
                        <Button
                          variant="contained"
                          sx={{
                            // marginRight: "16px",
                            textTransform: "none",
                            borderRadius: "5px 25px 25px 5px",
                            background: "#D77D46",
                            color: "#FFFFFF",
                            // maxHeight: "25.48px",
                            paddingTop: "8px !important",
                            paddingBottom: "7px !important",
                          }}
                        >
                          Subscribe
                        </Button>
                      ),
                    }}
                  />

                  <Stack
                    className="footer-legal-links"
                    direction="row"
                    spacing={3}
                    justifyContent={{ xs: "flex-start", md: "flex-end" }}
                    mt={2}
                  >
                    {[
                      "Privacy Policy",
                      "Terms & Conditions",
                      "Cookie Policy",
                    ].map((text) => (
                      <Typography
                        key={text}
                        variant="body2"
                        sx={{ cursor: "pointer" }}
                      >
                        {text}
                      </Typography>
                    ))}
                  </Stack>
                </Box>
              </Box>
            </Box>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
};

export default Footer;
