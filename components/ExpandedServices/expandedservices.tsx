"use client";

import { Box, Typography } from "@mui/material";
import React from "react";
import "./expandedservices.scss";
import Image, { StaticImageData } from "next/image";

interface ExpandedServicesProps {
  icon: StaticImageData | string;
  header: string;
  description: string;
}

const ExpandedServices = ({
  icon,
  header,
  description,
}: ExpandedServicesProps) => {
  return (
    <Box className = "expanded-services">
      <Box className = "expanded-services-icon">
        <Box className = "expanded-services-icon-box">
            <Image src={icon} alt={header} width={80} height={80} className="image"/>
        </Box>
      </Box>
      <Box className = "expanded-services-content">
        <Typography variant="h6" className = "expanded-services-content-header">{header}</Typography>
        <Typography variant="body1" className = "expanded-services-content-description">{description}</Typography>
      </Box>
    </Box>
  );
};

export default ExpandedServices;
