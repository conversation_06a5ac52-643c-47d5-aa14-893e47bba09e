@use "../../styles/variables" as *;

.expanded-services {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: $text-white;

  .expanded-services-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 30px 0px 5px;

    .expanded-services-icon-box {
      width: 100px;
      height: 100px;

    }

    
  }

  .expanded-services-content {
    font-size: 20px;
    line-height: 30px;

    .expanded-services-content-header {
      font-size: 20px;
      font-weight: 600;
    }

    .expanded-services-content-description {
      margin-top: 10px;
      font-size: 20px;
      font-weight: 400;
      text-align: justify;
    }
  }
}
