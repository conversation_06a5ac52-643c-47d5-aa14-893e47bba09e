import React from "react";
import "@/components/OurValues/OurValues.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { OurValues_Img } from "@/public/index";

const OurValues: React.FC = () => {
  return (
    <Box className="our-values-section-container">
      <Typography variant="h2" className="our-values-title">
        Our Values
      </Typography>
      <Image
        src={OurValues_Img}
        alt="Our Values Section Image"
        // fill
        style={{
          objectFit: "cover",
          borderRadius: "8px",
          // transition: "transform 0.3s ease",
        }}
      />
      <Typography variant="body1" className="our-values-description">
        Our values are the foundation of everything we do. They guide our
        decisions, define our culture, and shape the way we work with clients
        and each other
      </Typography>
    </Box>
  );
};

export default OurValues;
