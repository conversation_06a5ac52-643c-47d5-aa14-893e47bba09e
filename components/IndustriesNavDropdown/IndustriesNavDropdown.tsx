import React from "react";
import { Box } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import "./IndustriesNavDropdown.scss";

interface IndustriesNavDropdownProps {
  data: Array<{
    title: string;
    image: any;
    route: string;
  }>;
  onClose: () => void;
}

const IndustriesNavDropdown: React.FC<IndustriesNavDropdownProps> = ({
  data,
  onClose,
}) => {
  return (
    <Box className="industries-dropdown-container" onMouseLeave={onClose}>
      <Box className="industries-dropdown-content">
        {data.map((item, index) => (
          <Box key={index} className="industry-item">
            <Link href={item.route} onClick={onClose}>
              <Box className="industry-image-container">
                <Image
                  src={item.image}
                  alt={item.title}
                  width={461}
                  height={228}
                  className="industry-image"
                />
              </Box>
            </Link>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default IndustriesNavDropdown;
