@use '../../styles/variables' as *;

.industries-dropdown-container {
    // border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-width: 1351px;
    min-height: 679.72px;
    padding: 16px;

    .industries-dropdown-content {
        display: flex;
        flex-wrap: wrap;
        // gap: 16px;
        justify-content: flex-start;
    }

    .industry-item {
        flex: 0 0 calc(33.33% - 11px);
    }

    .industry-image-container {
        position: relative;
        cursor: pointer;
        transition: transform 0.3s ease;
        // border-radius: 8px;
        overflow: hidden;
        width: 100%;
        // height: 160px;

        // &:hover {
        //     transform: translateY(-5px);

        // }
    }

    .industry-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        // border-radius: 8px;
    }


}