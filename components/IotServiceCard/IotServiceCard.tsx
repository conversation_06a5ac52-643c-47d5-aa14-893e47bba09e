"use client";

import React from "react";
import "./IotServiceCard.scss";
import { Box, Typography } from "@mui/material";

interface IotServiceCardProps {
  title: string;
  description: string;
}

const IotServiceCard: React.FC<IotServiceCardProps> = ({
  title,
  description,
}) => {
  return (
    <Box className="iot-service-card">
      <Typography className="iot-service-card-title">{title}</Typography>
      <Box sx={{margin: "26px 10px 10px 20px"}}>
      <Typography className="iot-service-card-description">
        {description}
      </Typography>
      </Box>
    </Box>
  );
};

export default IotServiceCard;
