"use client";
import React, { useState } from "react";
import "@/components/weBuildSection/weBuildSection.scss";
import { Typography, Box } from "@mui/material";
import CustomAccordion from "@/components/CustomAccordion/CustomAccordion";

interface WeBuildAccordionProps {
  items: string[];
}

const WeBuildAccordion: React.FC<WeBuildAccordionProps> = ({ items }) => {
  const [expandedPanel, setExpandedPanel] = useState<number | false>(false);

  const handleAccordionChange = (panel: number) => {
    setExpandedPanel(expandedPanel === panel ? false : panel);
  };

  return (
    <Box className="we-build-accordion-container">
      <Typography className="we-build-title" variant="h6" data-aos="fade-up">
       <span style={{ color: "#0fffb7" }}> What </span> We Do ?
      </Typography>

      <Typography
        className="we-build-description"
        data-aos="fade-up"
        data-aos-delay="100"
      >
        We build innovative and intelligent business solutions across Web,
        Mobile, and Digital Platforms, leveraging the latest technology
        stacks—including AI, ML, IoT, Cloud, Blockchain, and more. We are driven
        by a vision to create a better world through cutting-edge, impactful,
        and future-ready solutions.
      </Typography>

      {items.map((label, index) => (
        <CustomAccordion
          key={index}
          label={label}
          index={index}
          defaultExpanded={false}
          disabled={false}
          expanded={expandedPanel === index}
          onChange={handleAccordionChange}
        />
      ))}
    </Box>
  );
};

export default WeBuildAccordion;
