@use "../../styles/variables" as *;

.heating-ventilation-services-container {
  width: 100%;

  .heating-ventilation-services-grid {
    display: grid;
    grid-template-columns: repeat(2, 556px);
    gap: 40px;
    justify-content: center;
    margin: 0 auto;
    padding: 0 20px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
      gap: 40px;
      padding: 0 20px;

      .heating-ventilation-service-card {
        width: 100%;
        min-width: 556px;
        height: auto;
        min-height: 319px;
        margin: 0 auto;
      }
    }

    @media (max-width: 768px) {
      gap: 20px;
      padding: 0 15px;

      .heating-ventilation-service-card {
        max-width: 100%;
        min-height: 280px;
      }
    }
  }
}

.heating-ventilation-service-card {
  background-color: #093246b2;
  border: 1px solid #2499e2;
  padding: 30px 25px;
  min-height: 300px;
  transition: all 0.3s ease;

  .heating-ventilation-service-title {
    font-family: Poppins;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0%;
    text-align: center;
    background: #8cffe4;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    width: 349px;
    height: 55px;
    margin: 34px auto 25px auto;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: 768px) {
      font-size: 18px;
      line-height: 26px;
      margin-bottom: 20px;
      width: auto;
      height: auto;
    }
  }

  .heating-ventilation-service-list-container {
    min-width: 457px;
    .heating-ventilation-service-list {
      .heating-ventilation-service-item {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        color: $text-white;
        display: flex;
        align-items: flex-start;
        // padding: 8px 0;
        margin: 0;

        &::before {
          content: "•";
          color: #ffffff;
          font-weight: bold;
          margin-right: 12px;
          margin-top: 2px;
          flex-shrink: 0;
        }

        @media (max-width: 768px) {
          font-size: 15px;
          line-height: 26px;
          padding: 6px 0;
        }

        @media (max-width: 480px) {
          font-size: 14px;
          line-height: 24px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 25px 20px;
    min-height: 250px;
  }

  @media (max-width: 480px) {
    padding: 20px 15px;
    min-height: 200px;
  }
}
