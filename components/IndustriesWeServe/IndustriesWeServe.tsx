import React from "react";
import "@/components/IndustriesWeServe/IndustriesWeServe.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  IndWeServe_Img_1,
  IndWeServe_Img_2,
  IndWeServe_Img_3,
  IndWeServe_Img_4,
  IndWeServe_Img_5,
  IndWeServe_Img_6,
} from "@/public/index";

const IndustriesWeServe: React.FC = () => {
  const industryImages = [
    { img: IndWeServe_Img_1, label: "Smart Homes & Buildings" },
    { img: IndWeServe_Img_2, label: "Industrial Machinery" },
    { img: IndWeServe_Img_3, label: "Sensor Teach Wearables" },
    { img: IndWeServe_Img_4, label: "Smart Metering" },
    { img: IndWeServe_Img_5, label: "Smart Farming" },
    { img: IndWeServe_Img_6, label: "Heating & Ventilation" },
  ];

  return (
    <Box className="industries-we-serve-container">
      <Typography className="industries-title" variant="h5">
        Industries We Serve
      </Typography>

      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          gap: 3,
          justifyContent: "center",
          // maxWidth: "1200px",
          margin: "0 auto",
          padding: "2rem",
          paddingBottom: "86px",
        }}
      >
        {industryImages.map((industry, index) => (
          <Box
            key={index}
            sx={{
              // width: { xs: "100%", sm: "calc(33.333% - 24px)" },
              width:"140px",
              position: "relative",
              // height: "250px",
              borderRadius: "8px",
              overflow: "hidden",
              "&:hover img": {
                transform: "scale(1.05)",
              },
            }}
          >
            <Image
              src={industry.img}
              alt={industry.label}
              // fill
              style={{
                objectFit: "cover",
                borderRadius: "8px",
                cursor: "pointer",
                transition: "transform 0.3s ease",
              }}
            />
            {/* <Box
              sx={{
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                background:
                  "linear-gradient(to top, rgba(0,0,0,0.7), transparent)",
                padding: "20px",
              }}
            >
              <Typography
                className="industry-label"
                variant="subtitle1"
                sx={{
                  textAlign: "center",
                  fontWeight: 500,
                  color: "#bfffe5",
                }}
              >
                {industry.label}
              </Typography>
            </Box> */}
          </Box>
        ))}
      </Box>

      <Typography className="industries-description">
        Our engineering team have enabled businesses to go beyond traditional
        business models, enhanced responsiveness and efficiency. By integrating
        advanced technologies like AI, automation, and cloud platforms, we help
        our clients exceed customer expectations, streamline operations, and
        create memorable, value-driven experiences.
      </Typography>
    </Box>
  );
};

export default IndustriesWeServe;
