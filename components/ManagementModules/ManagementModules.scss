.management-modules {
  width: 100%;

  .management-modules-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 0 auto;
    // padding: 0 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .management-module-card {
    background: #021f2e;
    border: 1px solid #2499e280;
    padding: 38px 28px 25px 35px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      border-color: #4a9eff;
    }

    .management-module-title {
      margin-bottom: 25px;
      text-align: center;
      font-family: Poppins;
      font-weight: 400;
      font-size: 26px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: center;
      color: #8cffe4;
      vertical-align: middle;

      @media (max-width: 768px) {
        font-size: 1.3rem;
      }
    }

    .management-module-description {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
      text-align: justify;

      @media (max-width: 768px) {
        font-size: 0.9rem;
        text-align: left;
      }
    }
  }
}
