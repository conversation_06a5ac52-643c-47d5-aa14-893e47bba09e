"use client";

import React from "react";
import { Box, Typography } from "@mui/material";
import "./ManagementModules.scss";

const ManagementModules = () => {
  const modules = [
    {
      title: "Employee Management",
      description:
        "Our platform centralize the overall organization human resource ecosystem and automates the storage, retrieval, and reporting of critical HR data, enhancing efficiency and decision making. Platforms enable management to maintain consistent communication with the employees, ensuring transparency and engagement throughout the employee life cycle.",
    },
    {
      title: "Operation Management",
      description:
        "System play a critical role in streamlining workflows, managing supply chains, and optimizing resource use. Platform oversees the, designing and managing the operation processes to ensure efficient production and delivery of goods or services. Business leaders can automate routine tasks, monitor performance metrics, and support strategic decision-making.",
    },
    {
      title: "Finance & Payroll",
      description:
        "Platform has integrated finance and payroll solutions providing employees self-service access to the financial and compensation information with provide transparency. Systems automate these processes and guides business leaders to take quality decisions by providing insights into financial performance and resource allocation.",
    },
    {
      title: "IT Ticketing System",
      description:
        "Our solution enables IT managers to efficiently track and resolve technical issues and service requests by improving communication between IT teams and end-users. The system automates ticket routing, real-time status updates, SLA tracking, with comprehensive dashboards ensuring a streamlined and proactive IT support process.",
    },
    {
      title: "Project Management",
      description:
        "Our Project Management module streamlines task scheduling, resource allocation, progress tracking, risk management, and team collaboration. The system offers real-time visibility into project timelines and deliverables, enabling proactive decision-making and greater accountability by centralizing project data and communication.",
    },
    {
      title: "Marketing Campaigning",
      description:
        "The platform empowers business managers to automate repetitive tasks, enhance engagement, personalize outreach, and maximize ROI. With robust features like email marketing, social media integration, and customizable dashboards, it enables users to efficiently plan, execute, and monitor multi-channel marketing campaigns for greater impact.",
    },
  ];

  return (
    <Box className="management-modules">
      <Box className="management-modules-grid">
        {modules.map((module, index) => (
          <Box key={index} className="management-module-card">
            <Typography className="management-module-title">
              {module.title}
            </Typography>
            <Typography className="management-module-description">
              {module.description}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ManagementModules;
