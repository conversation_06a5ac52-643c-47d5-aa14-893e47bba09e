@use '../../styles/variables' as *;

.appbar-container {
   // min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $background-dark !important;
    padding: 0 70px;

    .container-box {
        max-width: 100%;

        .toolbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .nav-button {
                color: $text-white;
                font-weight: 600;
                font-family: $font-poppins;
                font-size: 16px;
                line-height: 30px;
                border-radius: 10px;
                text-transform: none;
            }
        }
    }
}

.dropdown-menu {
    background-color: #021f2e;

    .MuiMenuItem-root {
        color: white;
        font-family: Poppins;
        font-size: 15px;

        &:hover {
            background-color: #d77d46;
            color: white;
        }
    }
}

/* Mobile Menu Styles */
@media screen and (max-width: 1200px) {
    .appbar-container {
        .container-box {
            .toolbar-container {
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }
    }

    .MuiDrawer-paper {
        .nav-button {
            margin: 8px 0;
            width: 100%;
            justify-content: flex-start;
            padding: 12px;

            &:hover {
                background-color: rgba(215, 125, 70, 0.1);
            }
        }
    }
}