"use client";
import React from "react";
import "@/components/heroSection/heroSection.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectFade, Autoplay, Navigation, Pagination } from "swiper/modules";
import { heroCarousal_1, AR_VR_Img1,ai_visiontek1 } from "@/public/index";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-fade";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Box, Typography } from "@mui/material";
import Image from "next/image";

const slides = [
  {
    id: 1,
    image: heroCarousal_1,
  },
  {
    id: 2,
    image: AR_VR_Img1,
  },
  {
    id: 3,
    image: ai_visiontek1,
  },
];

const HeroSection = () => {
  return (
    <Box className="hero-section-container">
      <section className="swiper-section-container">
        <Swiper
          modules={[EffectFade, Autoplay, Navigation, Pagination]}
          effect="fade"
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          navigation={true}
          pagination={{ clickable: true }}
          speed={1000}
          loop={true}
          className="swiper-container"
        >
          {slides.map((slide) => (
            <SwiperSlide key={slide.id}>
              <div className="slide-image-container">
                <Image
                  src={slide.image}
                  alt="Hero Carousel"
                  fill
                  style={{ objectFit: "cover" }}
                  priority={slide.id === 1}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </section>

      <Box className="hero-section-content">
        <Typography className="hero-section-text">
          Aadvik TekLabs is a team of visionary thinkers, tech innovators, and
          passionate problem-solvers, who are there to redefine the business
          boundaries in the field of AR VR MR technology and AIoT domain. Our
          mission is to harness the power of AIoT and vision-based technologies,
          use the same to solve the business problems and deliver the
          technological advantages to our customers. Team is highly experienced
          in Embedded, AIoT and AR VR MR technology & working in the field of
          Energy, T&D, Lighting and Industrial Machinery domain.
        </Typography>
      </Box>
    </Box>
  );
};

export default HeroSection;
