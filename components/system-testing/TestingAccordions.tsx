"use client";

import React, { useState } from "react";
import CustomAccordion from "@/components/CustomAccordion/CustomAccordion";
import { TestingOurSpecialization } from "@/constant/index";

const TestingAccordions: React.FC = () => {
  const [expandedPanel, setExpandedPanel] = useState<number | false>(false);

  const handleChange = (panel: number) => {
    setExpandedPanel(expandedPanel === panel ? false : panel);
  };

  return (
    <>
      {TestingOurSpecialization.map((item, index) => (
        <CustomAccordion
          key={index}
          label={item.accordionLabel}
          details={item.details}
          index={index}
          defaultExpanded={false}
          disabled={false}
          expanded={expandedPanel === index}
          onChange={handleChange}
        />
      ))}
    </>
  );
};

export default TestingAccordions;
