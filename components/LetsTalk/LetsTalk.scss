@use '../../styles/variables' as *;

.lets-talk-container {
    margin: 0px 77px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 99px;

    .lets-talk-title {
        font-family: $font-prata !important;
        font-weight: 400;
        font-size: 64px;
        line-height: 70px;
        letter-spacing: 0%;
        background: $hero-gradient;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        padding-bottom: 99px;
        transition: all 0.3s ease;
    }

    .lets-talk-description {
        font-family: var(--font-montserrat);
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #FFFFFF;
        margin-bottom: 99px;

    }

    .lets-talk-form {
        width: 100%;
        display: flex;
        justify-content: space-around;

        .lets-talk-form-header {
            display: flex;
            flex-direction: column;
            align-items: center;

            label {
                font-weight: 400;
                font-size: 20px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: #FFFFFF;

            }
        }
    }

    .lets-talk-textarea-container {
        // max-width: 100%;
        width: 100%;
        margin: auto;
        margin-top: 57px;
        margin-bottom: 139px;

        .lets-talk-textarea {
            border: 0.5px solid #D9D9D94A;
            width: 100%;
            background-color: #122638;
            border-radius: 10px;
            padding: 1rem;
            color: #ccc;
            font-family: inherit;
            font-size: 1rem;
            background: transparent;
            color: #fff;
            resize: none;


            &::placeholder {
                text-align: center;
                line-height: 140px;
                /* Adjust this value to vertically center the placeholder */
                color: #aaa;
                font-size: 14px;
            }

            &:focus {
                outline: none;
                border-color: #6787ff;

                &::placeholder {
                    opacity: 0.5;
                }
            }
        }

        .lets-talk-buttons {
            display: flex;
            justify-content: right;
            margin-top: 46px;
            gap: 46px;

            .lets-talk-discard-button {
                font-family: var(--font-montserrat);
                font-weight: 400;
                font-size: 24px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                text-transform: none;
                background: #D9D9D9;
                color: #021F2E;
                border-radius: 10px;

            }

            .lets-talk-submit-button {
                font-family: var(--font-montserrat);
                font-weight: 400;
                font-size: 24px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                text-transform: none;
                background: #D77D46;
                color: #021F2E;
                border-radius: 10px;


            }
        }
    }




}