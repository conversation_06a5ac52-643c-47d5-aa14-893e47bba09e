.custom-accordion-item {
  font-family: Poppins;
  font-weight: 400;
  font-size: 48px;
  padding-left: 0px !important;
  letter-spacing: 0%;
  background: transparent;
  box-shadow: none;
  border-bottom: 0.5px solid #8cffe4;

  .custom-accordion-summary {
    border-bottom: 0.5px solid #8cffe4;
    .MuiAccordionSummary-content {
      margin: 20px 0px;
    }

    .custom-accordion-label {
      font-weight: 400;
      font-size: 40px;
      letter-spacing: 0%;
      font-style: var(
        --font-style,
        normal
      );
      background: var(--label-color, none);
      background: var(
        --label-color,
        var(
          --background-gradient,
          radial-gradient(50% 80% at 50% 50%, #8cffe4 30%, #404a78 100%)
        )
      );
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: var(--label-color, transparent);
    }
  }

  .custom-accordion-details {
    .detail-item {
      .detail-title {
        font-family: Poppins;
        font-weight: 600;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        color: #ffffff;
        margin-bottom: 22px;
      }

      .detail-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        margin-bottom: 52px;
      }

      .bullets-points {
        padding: 0;
        margin-bottom: 30px;

        .bullet-point {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          color: #ffffff;
          padding-left: 20px;
          position: relative;

          &::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #ffffff;
            font-size: 20px;
          }

          .MuiListItemText-primary {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            color: #ffffff;
          }
        }
      }

      .caption {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 30px;
      }
    }
  }
}