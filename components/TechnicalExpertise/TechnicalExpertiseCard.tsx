import "./TechnicalExpertiseCard.scss";
import { Box, List, ListItem, Typography } from "@mui/material";

interface TechnicalExpertiseCardProps {
  title: string;
  bulletPoints: string[];
}

const TechnicalExpertiseCard = ({
  title,
  bulletPoints,
}: TechnicalExpertiseCardProps) => {
  return (
    <Box className="technical-expertise-item">
      <Typography
        className="technical-expertise-item-title"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        {title}
      </Typography>
      <List className="technical-expertise-item-list" data-aos="fade-up" data-aos-duration="1000">
        {bulletPoints.map((point, index) => (
          <ListItem key={index} className="technical-expertise-item-list-item">
            {point}
          </ListItem>
        ))}
      </List>
    </Box>
  );
};

export default TechnicalExpertiseCard;
